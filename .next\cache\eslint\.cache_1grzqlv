[{"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\page.tsx": "1", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\[id]\\page.tsx": "2", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cennik\\page.tsx": "3", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cookies\\page.tsx": "4", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\dla-firm\\page.tsx": "5", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\faq\\page.tsx": "6", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\galeria\\page.tsx": "7", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\kontakt\\page.tsx": "8", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\layout.tsx": "9", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\o-nas\\page.tsx": "10", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\opinie\\page.tsx": "11", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\polityka-prywatnosci\\page.tsx": "13", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\regulamin\\page.tsx": "14", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\robots.ts": "15", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\rodo\\page.tsx": "16", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\sitemap.ts": "17", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\cennik\\page.tsx": "18", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\dezynfekcja-po-smierci\\page.tsx": "19", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie\\page.tsx": "20", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie-pomieszczen\\page.tsx": "21", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\page.tsx": "22", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-pozarach\\page.tsx": "23", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-zalaniach\\page.tsx": "24", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-pozarach\\page.tsx": "25", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-zgonach\\page.tsx": "26", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-skutkow-powodzi\\page.tsx": "27", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-zapachow\\page.tsx": "28", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\AboutHero.tsx": "29", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\CompanyHistory.tsx": "30", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\MissionValues.tsx": "31", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\TeamSection.tsx": "32", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BContact.tsx": "33", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BHero.tsx": "34", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BPartners.tsx": "35", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BProcess.tsx": "36", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BServices.tsx": "37", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogCategories.tsx": "38", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogGrid.tsx": "39", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogHero.tsx": "40", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogNewsletter.tsx": "41", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\CertificationsSection.tsx": "42", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Chatbot.tsx": "43", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactForm.tsx": "44", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactHero.tsx": "45", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactInfo.tsx": "46", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactMap.tsx": "47", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\EmergencyContact.tsx": "48", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQContact.tsx": "49", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQHero.tsx": "50", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQSection.tsx": "51", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Footer.tsx": "52", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryFilters.tsx": "53", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryGrid.tsx": "54", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryHero.tsx": "55", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryStats.tsx": "56", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Header.tsx": "57", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\HeroSection.tsx": "58", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\LanguageSwitcher.tsx": "59", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingCalculator.tsx": "60", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFactors.tsx": "61", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFAQ.tsx": "62", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingHero.tsx": "63", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingTables.tsx": "64", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsFilters.tsx": "65", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsForm.tsx": "66", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsGrid.tsx": "67", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsHero.tsx": "68", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsStats.tsx": "69", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\SEOSchema.tsx": "70", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\ServicesOverview.tsx": "71", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\TestimonialsPreview.tsx": "72", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\WhyChooseUs.tsx": "73", "C:\\Users\\<USER>\\Downloads\\test2\\src\\hooks\\useTranslation.ts": "74", "C:\\Users\\<USER>\\Downloads\\test2\\src\\lib\\i18n.ts": "75"}, {"size": 616, "mtime": 1751976320944, "results": "76", "hashOfConfig": "77"}, {"size": 10508, "mtime": 1751976383229, "results": "78", "hashOfConfig": "77"}, {"size": 930, "mtime": 1751958327462, "results": "79", "hashOfConfig": "77"}, {"size": 11009, "mtime": 1751976652090, "results": "80", "hashOfConfig": "77"}, {"size": 863, "mtime": 1751942039027, "results": "81", "hashOfConfig": "77"}, {"size": 672, "mtime": 1751941786802, "results": "82", "hashOfConfig": "77"}, {"size": 802, "mtime": 1751976253284, "results": "83", "hashOfConfig": "77"}, {"size": 868, "mtime": 1751941887017, "results": "84", "hashOfConfig": "77"}, {"size": 2486, "mtime": 1751962144753, "results": "85", "hashOfConfig": "77"}, {"size": 939, "mtime": 1751941603543, "results": "86", "hashOfConfig": "77"}, {"size": 893, "mtime": 1751960640022, "results": "87", "hashOfConfig": "77"}, {"size": 588, "mtime": 1751941400847, "results": "88", "hashOfConfig": "77"}, {"size": 7689, "mtime": 1752281635525, "results": "89", "hashOfConfig": "77"}, {"size": 9316, "mtime": 1751976470034, "results": "90", "hashOfConfig": "77"}, {"size": 270, "mtime": 1751942136665, "results": "91", "hashOfConfig": "77"}, {"size": 10397, "mtime": 1751976601627, "results": "92", "hashOfConfig": "77"}, {"size": 2494, "mtime": 1751942128637, "results": "93", "hashOfConfig": "77"}, {"size": 875, "mtime": 1752277901058, "results": "94", "hashOfConfig": "77"}, {"size": 13693, "mtime": 1752277452957, "results": "95", "hashOfConfig": "77"}, {"size": 18697, "mtime": 1752273055949, "results": "96", "hashOfConfig": "77"}, {"size": 18767, "mtime": 1752277672145, "results": "97", "hashOfConfig": "77"}, {"size": 11256, "mtime": 1751941771535, "results": "98", "hashOfConfig": "77"}, {"size": 13961, "mtime": 1752272924211, "results": "99", "hashOfConfig": "77"}, {"size": 15294, "mtime": 1752272985335, "results": "100", "hashOfConfig": "77"}, {"size": 13960, "mtime": 1752277517822, "results": "101", "hashOfConfig": "77"}, {"size": 11454, "mtime": 1752272869769, "results": "102", "hashOfConfig": "77"}, {"size": 16922, "mtime": 1752277590251, "results": "103", "hashOfConfig": "77"}, {"size": 20150, "mtime": 1752277761373, "results": "104", "hashOfConfig": "77"}, {"size": 3395, "mtime": 1751941621472, "results": "105", "hashOfConfig": "77"}, {"size": 5945, "mtime": 1751941679427, "results": "106", "hashOfConfig": "77"}, {"size": 6136, "mtime": 1752282200868, "results": "107", "hashOfConfig": "77"}, {"size": 8549, "mtime": 1751941715616, "results": "108", "hashOfConfig": "77"}, {"size": 14933, "mtime": 1751976546482, "results": "109", "hashOfConfig": "77"}, {"size": 3654, "mtime": 1751942061000, "results": "110", "hashOfConfig": "77"}, {"size": 7130, "mtime": 1752282342417, "results": "111", "hashOfConfig": "77"}, {"size": 8918, "mtime": 1751948759624, "results": "112", "hashOfConfig": "77"}, {"size": 9979, "mtime": 1751976508362, "results": "113", "hashOfConfig": "77"}, {"size": 3928, "mtime": 1751976268501, "results": "114", "hashOfConfig": "77"}, {"size": 10456, "mtime": 1751976300764, "results": "115", "hashOfConfig": "77"}, {"size": 2490, "mtime": 1751958012370, "results": "116", "hashOfConfig": "77"}, {"size": 4912, "mtime": 1751958115442, "results": "117", "hashOfConfig": "77"}, {"size": 8058, "mtime": 1751941525567, "results": "118", "hashOfConfig": "77"}, {"size": 12063, "mtime": 1751960781951, "results": "119", "hashOfConfig": "77"}, {"size": 9805, "mtime": 1751941951409, "results": "120", "hashOfConfig": "77"}, {"size": 3310, "mtime": 1751941905607, "results": "121", "hashOfConfig": "77"}, {"size": 8537, "mtime": 1751941986115, "results": "122", "hashOfConfig": "77"}, {"size": 10960, "mtime": 1752282515934, "results": "123", "hashOfConfig": "77"}, {"size": 7815, "mtime": 1751941588425, "results": "124", "hashOfConfig": "77"}, {"size": 6381, "mtime": 1751941869753, "results": "125", "hashOfConfig": "77"}, {"size": 1838, "mtime": 1751941799191, "results": "126", "hashOfConfig": "77"}, {"size": 8040, "mtime": 1751941841186, "results": "127", "hashOfConfig": "77"}, {"size": 6429, "mtime": 1752277832116, "results": "128", "hashOfConfig": "77"}, {"size": 5095, "mtime": 1751976140487, "results": "129", "hashOfConfig": "77"}, {"size": 14837, "mtime": 1751976210767, "results": "130", "hashOfConfig": "77"}, {"size": 2465, "mtime": 1751958172121, "results": "131", "hashOfConfig": "77"}, {"size": 3804, "mtime": 1751958304390, "results": "132", "hashOfConfig": "77"}, {"size": 9853, "mtime": 1752277388184, "results": "133", "hashOfConfig": "77"}, {"size": 5096, "mtime": 1752281911072, "results": "134", "hashOfConfig": "77"}, {"size": 2515, "mtime": 1751962300012, "results": "135", "hashOfConfig": "77"}, {"size": 14364, "mtime": 1751960513747, "results": "136", "hashOfConfig": "77"}, {"size": 5045, "mtime": 1751976088545, "results": "137", "hashOfConfig": "77"}, {"size": 6103, "mtime": 1751976124112, "results": "138", "hashOfConfig": "77"}, {"size": 3125, "mtime": 1751958362870, "results": "139", "hashOfConfig": "77"}, {"size": 10513, "mtime": 1751960452647, "results": "140", "hashOfConfig": "77"}, {"size": 6971, "mtime": 1751961369388, "results": "141", "hashOfConfig": "77"}, {"size": 10462, "mtime": 1751961428706, "results": "142", "hashOfConfig": "77"}, {"size": 9228, "mtime": 1751960712183, "results": "143", "hashOfConfig": "77"}, {"size": 3027, "mtime": 1751960660134, "results": "144", "hashOfConfig": "77"}, {"size": 4994, "mtime": 1751961317897, "results": "145", "hashOfConfig": "77"}, {"size": 3539, "mtime": 1751942164899, "results": "146", "hashOfConfig": "77"}, {"size": 6149, "mtime": 1751941457132, "results": "147", "hashOfConfig": "77"}, {"size": 6601, "mtime": 1752281951428, "results": "148", "hashOfConfig": "77"}, {"size": 6256, "mtime": 1751941488513, "results": "149", "hashOfConfig": "77"}, {"size": 1075, "mtime": 1751962330282, "results": "150", "hashOfConfig": "77"}, {"size": 4731, "mtime": 1751960879677, "results": "151", "hashOfConfig": "77"}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fddo2y", {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cennik\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cookies\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\dla-firm\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\galeria\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\kontakt\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\o-nas\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\opinie\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\polityka-prywatnosci\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\regulamin\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\rodo\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\cennik\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\dezynfekcja-po-smierci\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie-pomieszczen\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-pozarach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-zalaniach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-pozarach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-zgonach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-skutkow-powodzi\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-zapachow\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\AboutHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\CompanyHistory.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\MissionValues.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\TeamSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BContact.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BPartners.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BProcess.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BServices.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogCategories.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogGrid.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogNewsletter.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\CertificationsSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Chatbot.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactForm.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactInfo.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactMap.tsx", ["377"], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\EmergencyContact.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQContact.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryFilters.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryGrid.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryStats.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\LanguageSwitcher.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingCalculator.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFactors.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFAQ.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingTables.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsFilters.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsForm.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsGrid.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsStats.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\SEOSchema.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\ServicesOverview.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\TestimonialsPreview.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\WhyChooseUs.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\hooks\\useTranslation.ts", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\lib\\i18n.ts", [], [], {"ruleId": "378", "severity": 1, "message": "379", "line": 17, "column": 9, "nodeType": "380", "endLine": 21, "endColumn": 4}, "react-hooks/exhaustive-deps", "The 'companyLocation' object makes the dependencies of useEffect Hook (at line 114) change on every render. To fix this, wrap the initialization of 'companyLocation' in its own useMemo() Hook.", "VariableDeclarator"]