[{"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\page.tsx": "1", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\[id]\\page.tsx": "2", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cennik\\page.tsx": "3", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cookies\\page.tsx": "4", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\dla-firm\\page.tsx": "5", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\faq\\page.tsx": "6", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\galeria\\page.tsx": "7", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\kontakt\\page.tsx": "8", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\layout.tsx": "9", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\o-nas\\page.tsx": "10", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\opinie\\page.tsx": "11", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\page.tsx": "12", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\polityka-prywatnosci\\page.tsx": "13", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\regulamin\\page.tsx": "14", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\robots.ts": "15", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\rodo\\page.tsx": "16", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\sitemap.ts": "17", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\cennik\\page.tsx": "18", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\dezynfekcja-po-smierci\\page.tsx": "19", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie\\page.tsx": "20", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie-pomieszczen\\page.tsx": "21", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\page.tsx": "22", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-pozarach\\page.tsx": "23", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-zalaniach\\page.tsx": "24", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-pozarach\\page.tsx": "25", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-zgonach\\page.tsx": "26", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-skutkow-powodzi\\page.tsx": "27", "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-zapachow\\page.tsx": "28", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\AboutHero.tsx": "29", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\CompanyHistory.tsx": "30", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\MissionValues.tsx": "31", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\TeamSection.tsx": "32", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BContact.tsx": "33", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BHero.tsx": "34", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BPartners.tsx": "35", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BProcess.tsx": "36", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BServices.tsx": "37", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogCategories.tsx": "38", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogGrid.tsx": "39", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogHero.tsx": "40", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogNewsletter.tsx": "41", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\CertificationsSection.tsx": "42", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Chatbot.tsx": "43", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactForm.tsx": "44", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactHero.tsx": "45", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactInfo.tsx": "46", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactMap.tsx": "47", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\EmergencyContact.tsx": "48", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQContact.tsx": "49", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQHero.tsx": "50", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQSection.tsx": "51", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Footer.tsx": "52", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryFilters.tsx": "53", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryGrid.tsx": "54", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryHero.tsx": "55", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryStats.tsx": "56", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Header.tsx": "57", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\HeroSection.tsx": "58", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\LanguageSwitcher.tsx": "59", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingCalculator.tsx": "60", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFactors.tsx": "61", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFAQ.tsx": "62", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingHero.tsx": "63", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingTables.tsx": "64", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsFilters.tsx": "65", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsForm.tsx": "66", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsGrid.tsx": "67", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsHero.tsx": "68", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsStats.tsx": "69", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\SEOSchema.tsx": "70", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\ServicesOverview.tsx": "71", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\TestimonialsPreview.tsx": "72", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\WhyChooseUs.tsx": "73", "C:\\Users\\<USER>\\Downloads\\test2\\src\\hooks\\useTranslation.ts": "74", "C:\\Users\\<USER>\\Downloads\\test2\\src\\lib\\i18n.ts": "75", "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\ChatbotWrapper.tsx": "76"}, {"size": 616, "mtime": 1751976320944, "results": "77", "hashOfConfig": "78"}, {"size": 10622, "mtime": 1752282857699, "results": "79", "hashOfConfig": "78"}, {"size": 946, "mtime": 1752330128897, "results": "80", "hashOfConfig": "78"}, {"size": 11009, "mtime": 1751976652090, "results": "81", "hashOfConfig": "78"}, {"size": 863, "mtime": 1751942039027, "results": "82", "hashOfConfig": "78"}, {"size": 680, "mtime": 1752330055609, "results": "83", "hashOfConfig": "78"}, {"size": 797, "mtime": 1752330074080, "results": "84", "hashOfConfig": "78"}, {"size": 884, "mtime": 1752330159935, "results": "85", "hashOfConfig": "78"}, {"size": 2531, "mtime": 1752330039555, "results": "86", "hashOfConfig": "78"}, {"size": 939, "mtime": 1751941603543, "results": "87", "hashOfConfig": "78"}, {"size": 909, "mtime": 1752330097980, "results": "88", "hashOfConfig": "78"}, {"size": 588, "mtime": 1751941400847, "results": "89", "hashOfConfig": "78"}, {"size": 7689, "mtime": 1752281635525, "results": "90", "hashOfConfig": "78"}, {"size": 9316, "mtime": 1751976470034, "results": "91", "hashOfConfig": "78"}, {"size": 270, "mtime": 1751942136665, "results": "92", "hashOfConfig": "78"}, {"size": 10397, "mtime": 1751976601627, "results": "93", "hashOfConfig": "78"}, {"size": 2494, "mtime": 1751942128637, "results": "94", "hashOfConfig": "78"}, {"size": 875, "mtime": 1752277901058, "results": "95", "hashOfConfig": "78"}, {"size": 13693, "mtime": 1752277452957, "results": "96", "hashOfConfig": "78"}, {"size": 18697, "mtime": 1752273055949, "results": "97", "hashOfConfig": "78"}, {"size": 18767, "mtime": 1752277672145, "results": "98", "hashOfConfig": "78"}, {"size": 11256, "mtime": 1751941771535, "results": "99", "hashOfConfig": "78"}, {"size": 13961, "mtime": 1752272924211, "results": "100", "hashOfConfig": "78"}, {"size": 15294, "mtime": 1752272985335, "results": "101", "hashOfConfig": "78"}, {"size": 13960, "mtime": 1752277517822, "results": "102", "hashOfConfig": "78"}, {"size": 11454, "mtime": 1752272869769, "results": "103", "hashOfConfig": "78"}, {"size": 16922, "mtime": 1752277590251, "results": "104", "hashOfConfig": "78"}, {"size": 20150, "mtime": 1752277761373, "results": "105", "hashOfConfig": "78"}, {"size": 3409, "mtime": 1752326058692, "results": "106", "hashOfConfig": "78"}, {"size": 5959, "mtime": 1752326069487, "results": "107", "hashOfConfig": "78"}, {"size": 6150, "mtime": 1752326082356, "results": "108", "hashOfConfig": "78"}, {"size": 8563, "mtime": 1752326094036, "results": "109", "hashOfConfig": "78"}, {"size": 14933, "mtime": 1751976546482, "results": "110", "hashOfConfig": "78"}, {"size": 3668, "mtime": 1752326010227, "results": "111", "hashOfConfig": "78"}, {"size": 7144, "mtime": 1752326021929, "results": "112", "hashOfConfig": "78"}, {"size": 8932, "mtime": 1752326033720, "results": "113", "hashOfConfig": "78"}, {"size": 9987, "mtime": 1752326046998, "results": "114", "hashOfConfig": "78"}, {"size": 3928, "mtime": 1751976268501, "results": "115", "hashOfConfig": "78"}, {"size": 10456, "mtime": 1751976300764, "results": "116", "hashOfConfig": "78"}, {"size": 2490, "mtime": 1751958012370, "results": "117", "hashOfConfig": "78"}, {"size": 4912, "mtime": 1751958115442, "results": "118", "hashOfConfig": "78"}, {"size": 8072, "mtime": 1752326106676, "results": "119", "hashOfConfig": "78"}, {"size": 12063, "mtime": 1751960781951, "results": "120", "hashOfConfig": "78"}, {"size": 9805, "mtime": 1751941951409, "results": "121", "hashOfConfig": "78"}, {"size": 3324, "mtime": 1752326195623, "results": "122", "hashOfConfig": "78"}, {"size": 8545, "mtime": 1752326207284, "results": "123", "hashOfConfig": "78"}, {"size": 10990, "mtime": 1752282780769, "results": "124", "hashOfConfig": "78"}, {"size": 7829, "mtime": 1752326119371, "results": "125", "hashOfConfig": "78"}, {"size": 6395, "mtime": 1752325985023, "results": "126", "hashOfConfig": "78"}, {"size": 1852, "mtime": 1752325996742, "results": "127", "hashOfConfig": "78"}, {"size": 8040, "mtime": 1751941841186, "results": "128", "hashOfConfig": "78"}, {"size": 6443, "mtime": 1752326130874, "results": "129", "hashOfConfig": "78"}, {"size": 5095, "mtime": 1751976140487, "results": "130", "hashOfConfig": "78"}, {"size": 14837, "mtime": 1751976210767, "results": "131", "hashOfConfig": "78"}, {"size": 2479, "mtime": 1752325877799, "results": "132", "hashOfConfig": "78"}, {"size": 3818, "mtime": 1752325889095, "results": "133", "hashOfConfig": "78"}, {"size": 9853, "mtime": 1752277388184, "results": "134", "hashOfConfig": "78"}, {"size": 5110, "mtime": 1752326140808, "results": "135", "hashOfConfig": "78"}, {"size": 2515, "mtime": 1751962300012, "results": "136", "hashOfConfig": "78"}, {"size": 14364, "mtime": 1751960513747, "results": "137", "hashOfConfig": "78"}, {"size": 5059, "mtime": 1752325840973, "results": "138", "hashOfConfig": "78"}, {"size": 6103, "mtime": 1751976124112, "results": "139", "hashOfConfig": "78"}, {"size": 3139, "mtime": 1752325853517, "results": "140", "hashOfConfig": "78"}, {"size": 10527, "mtime": 1752325865634, "results": "141", "hashOfConfig": "78"}, {"size": 6971, "mtime": 1751961369388, "results": "142", "hashOfConfig": "78"}, {"size": 10462, "mtime": 1751961428706, "results": "143", "hashOfConfig": "78"}, {"size": 9213, "mtime": 1752325901350, "results": "144", "hashOfConfig": "78"}, {"size": 3041, "mtime": 1752325913579, "results": "145", "hashOfConfig": "78"}, {"size": 5008, "mtime": 1752325925120, "results": "146", "hashOfConfig": "78"}, {"size": 3553, "mtime": 1752326151605, "results": "147", "hashOfConfig": "78"}, {"size": 6163, "mtime": 1752326161939, "results": "148", "hashOfConfig": "78"}, {"size": 6615, "mtime": 1752326173335, "results": "149", "hashOfConfig": "78"}, {"size": 6264, "mtime": 1752326184286, "results": "150", "hashOfConfig": "78"}, {"size": 1075, "mtime": 1751962330282, "results": "151", "hashOfConfig": "78"}, {"size": 4731, "mtime": 1751960879677, "results": "152", "hashOfConfig": "78"}, {"size": 228, "mtime": 1752283160238, "results": "153", "hashOfConfig": "78"}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "fddo2y", {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\blog\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cennik\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\cookies\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\dla-firm\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\galeria\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\kontakt\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\o-nas\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\opinie\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\polityka-prywatnosci\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\regulamin\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\rodo\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\cennik\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\dezynfekcja-po-smierci\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\ozonowanie-pomieszczen\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-pozarach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\po-zalaniach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-pozarach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\sprzatanie-po-zgonach\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-skutkow-powodzi\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\usuwanie-zapachow\\page.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\AboutHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\CompanyHistory.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\MissionValues.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\about\\TeamSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BContact.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BPartners.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BProcess.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BServices.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogCategories.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogGrid.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\blog\\BlogNewsletter.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\CertificationsSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Chatbot.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactForm.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactInfo.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactMap.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\EmergencyContact.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQContact.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryFilters.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryGrid.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\gallery\\GalleryStats.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\LanguageSwitcher.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingCalculator.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFactors.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFAQ.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingTables.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsFilters.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsForm.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsGrid.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsHero.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\reviews\\ReviewsStats.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\SEOSchema.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\ServicesOverview.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\TestimonialsPreview.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\WhyChooseUs.tsx", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\hooks\\useTranslation.ts", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\lib\\i18n.ts", [], [], "C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\ChatbotWrapper.tsx", [], []]