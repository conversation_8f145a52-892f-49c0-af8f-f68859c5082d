{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [{"page": "/blog/[id]", "regex": "^/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/blog/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/cennik", "regex": "^/cennik(?:/)?$", "routeKeys": {}, "namedRegex": "^/cennik(?:/)?$"}, {"page": "/cookies", "regex": "^/cookies(?:/)?$", "routeKeys": {}, "namedRegex": "^/cookies(?:/)?$"}, {"page": "/dla-firm", "regex": "^/dla\\-firm(?:/)?$", "routeKeys": {}, "namedRegex": "^/dla\\-firm(?:/)?$"}, {"page": "/faq", "regex": "^/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/faq(?:/)?$"}, {"page": "/galeria", "regex": "^/galeria(?:/)?$", "routeKeys": {}, "namedRegex": "^/galeria(?:/)?$"}, {"page": "/kontakt", "regex": "^/kontakt(?:/)?$", "routeKeys": {}, "namedRegex": "^/kontakt(?:/)?$"}, {"page": "/o-nas", "regex": "^/o\\-nas(?:/)?$", "routeKeys": {}, "namedRegex": "^/o\\-nas(?:/)?$"}, {"page": "/opinie", "regex": "^/opinie(?:/)?$", "routeKeys": {}, "namedRegex": "^/opinie(?:/)?$"}, {"page": "/polityka-p<PERSON><PERSON><PERSON><PERSON>", "regex": "^/polityka\\-p<PERSON><PERSON><PERSON><PERSON>(?:/)?$", "routeKeys": {}, "namedRegex": "^/polityka\\-p<PERSON><PERSON><PERSON><PERSON>(?:/)?$"}, {"page": "/regulamin", "regex": "^/regulamin(?:/)?$", "routeKeys": {}, "namedRegex": "^/regulamin(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/rodo", "regex": "^/rodo(?:/)?$", "routeKeys": {}, "namedRegex": "^/rodo(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/uslugi", "regex": "^/uslugi(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi(?:/)?$"}, {"page": "/uslugi/cennik", "regex": "^/uslugi/cennik(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi/cennik(?:/)?$"}, {"page": "/uslugi/dezynfekcja-po-smierci", "regex": "^/uslugi/dezynfe<PERSON>\\-po\\-smie<PERSON>i(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi/dezynfe<PERSON>\\-po\\-smie<PERSON>i(?:/)?$"}, {"page": "/uslugi/ozonowanie", "regex": "^/uslugi/ozonowanie(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi/ozonowanie(?:/)?$"}, {"page": "/uslugi/ozonowanie-pomieszczen", "regex": "^/uslugi/ozonowanie\\-pomieszczen(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi/ozonowanie\\-pomieszczen(?:/)?$"}, {"page": "/uslugi/po-pozarach", "regex": "^/uslugi/po\\-pozarach(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi/po\\-pozarach(?:/)?$"}, {"page": "/uslugi/po-zalaniach", "regex": "^/uslugi/po\\-zalaniach(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi/po\\-zalaniach(?:/)?$"}, {"page": "/uslugi/sprzatanie-po-pozarach", "regex": "^/uslugi/sprzatanie\\-po\\-pozarach(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi/sprzatanie\\-po\\-pozarach(?:/)?$"}, {"page": "/uslugi/sprzatanie-po-zgonach", "regex": "^/uslugi/sprzatanie\\-po\\-zgonach(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi/sprzatanie\\-po\\-zgonach(?:/)?$"}, {"page": "/uslugi/usuwanie-skutkow-powodzi", "regex": "^/uslugi/usuwanie\\-skutkow\\-powodzi(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi/usuwanie\\-skutkow\\-powodzi(?:/)?$"}, {"page": "/uslugi/usuwanie-zapachow", "regex": "^/uslugi/usuwanie\\-zapachow(?:/)?$", "routeKeys": {}, "namedRegex": "^/uslugi/usuwanie\\-zapachow(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}