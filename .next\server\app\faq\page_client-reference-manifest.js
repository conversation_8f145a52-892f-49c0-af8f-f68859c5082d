globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/faq/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Chatbot.tsx":{"*":{"id":"(ssr)/./src/components/Chatbot.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(ssr)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HeroSection.tsx":{"*":{"id":"(ssr)/./src/components/HeroSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/pricing/PricingCalculator.tsx":{"*":{"id":"(ssr)/./src/components/pricing/PricingCalculator.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/pricing/PricingFAQ.tsx":{"*":{"id":"(ssr)/./src/components/pricing/PricingFAQ.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/b2b/B2BContact.tsx":{"*":{"id":"(ssr)/./src/components/b2b/B2BContact.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/faq/FAQSection.tsx":{"*":{"id":"(ssr)/./src/components/faq/FAQSection.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Chatbot.tsx":{"id":"(app-pages-browser)/./src/components/Chatbot.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Header.tsx":{"id":"(app-pages-browser)/./src/components/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\HeroSection.tsx":{"id":"(app-pages-browser)/./src/components/HeroSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingCalculator.tsx":{"id":"(app-pages-browser)/./src/components/pricing/PricingCalculator.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFAQ.tsx":{"id":"(app-pages-browser)/./src/components/pricing/PricingFAQ.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BContact.tsx":{"id":"(app-pages-browser)/./src/components/b2b/B2BContact.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\faq\\FAQSection.tsx":{"id":"(app-pages-browser)/./src/components/faq/FAQSection.tsx","name":"*","chunks":["app/faq/page","static/chunks/app/faq/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Downloads\\test2\\src\\":[],"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\page":[],"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\faq\\page":[]}}