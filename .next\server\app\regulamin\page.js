(()=>{var e={};e.id=818,e.ids=[818],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9301:(e,i,a)=>{"use strict";a.r(i),a.d(i,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>p,pages:()=>t,routeModule:()=>z,tree:()=>d}),a(4341),a(794),a(5866);var n=a(3191),s=a(8716),r=a(7922),l=a.n(r),o=a(5231),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);a.d(i,c);let d=["",{children:["regulamin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,4341)),"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\regulamin\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,794)),"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,5866,23)),"next/dist/client/components/not-found-error"]}],t=["C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\regulamin\\page.tsx"],p="/regulamin/page",h={require:a,loadChunk:()=>Promise.resolve()},z=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/regulamin/page",pathname:"/regulamin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7444:(e,i,a)=>{Promise.resolve().then(a.t.bind(a,9404,23))},3113:(e,i,a)=>{"use strict";a.d(i,{Z:()=>n});let n=(0,a(7162).Z)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},4341:(e,i,a)=>{"use strict";a.r(i),a.d(i,{default:()=>c});var n=a(9510),s=a(7371),r=a(3113),l=a(1319),o=a(7799);function c(){return(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[n.jsx("div",{className:"bg-primary-900 text-white py-16",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[n.jsx(r.Z,{className:"w-8 h-8"}),n.jsx("h1",{className:"text-3xl md:text-4xl font-bold",children:"Regulamin świadczenia usług"})]}),n.jsx("p",{className:"text-primary-200 text-lg",children:"Warunki wsp\xf3łpracy z SOLVICTUS Sp. z o.o."}),n.jsx("p",{className:"text-primary-300 text-sm mt-2",children:"Ostatnia aktualizacja: 15 stycznia 2024"})]})}),(0,n.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[n.jsx("div",{className:"bg-white rounded-xl shadow-lg p-8 md:p-12",children:(0,n.jsxs)("div",{className:"prose prose-lg max-w-none",children:[n.jsx("h2",{children:"\xa7 1. Postanowienia og\xf3lne"}),(0,n.jsxs)("ol",{children:[(0,n.jsxs)("li",{children:["Niniejszy regulamin określa zasady świadczenia usług przez",n.jsx("strong",{children:" SOLVICTUS Sp. z o.o."})," z siedzibą w Warszawie, ul. Przykładowa 123, 00-001 Warszawa, NIP: 1234567890."]}),n.jsx("li",{children:"Regulamin stanowi integralną część każdej umowy zawartej z Klientem."}),n.jsx("li",{children:"Akceptacja regulaminu następuje poprzez złożenie zam\xf3wienia lub podpisanie umowy."})]}),n.jsx("h2",{children:"\xa7 2. Definicje"}),(0,n.jsxs)("ol",{children:[(0,n.jsxs)("li",{children:[n.jsx("strong",{children:"Usługodawca"})," - SOLVICTUS Sp. z o.o."]}),(0,n.jsxs)("li",{children:[n.jsx("strong",{children:"Klient"})," - osoba fizyczna, prawna lub jednostka organizacyjna korzystająca z usług"]}),(0,n.jsxs)("li",{children:[n.jsx("strong",{children:"Usługi"})," - profesjonalne sprzątanie i dezynfekcja po traumatycznych wydarzeniach"]}),(0,n.jsxs)("li",{children:[n.jsx("strong",{children:"Miejsce świadczenia"})," - lokalizacja wskazana przez Klienta"]})]}),n.jsx("h2",{children:"\xa7 3. Zakres usług"}),(0,n.jsxs)("ol",{children:[(0,n.jsxs)("li",{children:["Usługodawca świadczy następujące usługi:",(0,n.jsxs)("ul",{children:[n.jsx("li",{children:"Sprzątanie po zgonach"}),n.jsx("li",{children:"Dezynfekcja biologiczna"}),n.jsx("li",{children:"Sprzątanie po pożarach"}),n.jsx("li",{children:"Usuwanie skutk\xf3w powodzi"}),n.jsx("li",{children:"Ozonowanie pomieszczeń"}),n.jsx("li",{children:"Usuwanie zapach\xf3w"})]})]}),n.jsx("li",{children:"Szczeg\xf3łowy zakres usług określany jest w ofercie lub umowie."}),n.jsx("li",{children:"Usługodawca zastrzega sobie prawo do odmowy świadczenia usług w przypadkach wykraczających poza jego kompetencje lub możliwości techniczne."})]}),n.jsx("h2",{children:"\xa7 4. Zam\xf3wienia i wyceny"}),(0,n.jsxs)("ol",{children:[(0,n.jsxs)("li",{children:["Zam\xf3wienia można składać:",(0,n.jsxs)("ul",{children:[n.jsx("li",{children:"Telefonicznie: +48 123 456 789"}),n.jsx("li",{children:"Email: <EMAIL>"}),n.jsx("li",{children:"Przez formularz na stronie internetowej"})]})]}),n.jsx("li",{children:"Każde zam\xf3wienie wymaga bezpłatnej wyceny na miejscu."}),n.jsx("li",{children:"Wycena jest ważna przez 30 dni od daty sporządzenia."}),n.jsx("li",{children:"Cena może ulec zmianie tylko w przypadku wykrycia dodatkowych problem\xf3w niewidocznych podczas wyceny."})]}),n.jsx("h2",{children:"\xa7 5. Realizacja usług"}),(0,n.jsxs)("ol",{children:[n.jsx("li",{children:"Usługi świadczone są przez wykwalifikowany personel z odpowiednimi certyfikatami i ubezpieczeniem."}),n.jsx("li",{children:"Klient zobowiązuje się zapewnić dostęp do miejsca świadczenia usług."}),n.jsx("li",{children:"W przypadku prac wymagających wyłączenia medi\xf3w, Klient zostanie o tym poinformowany wcześniej."}),n.jsx("li",{children:"Po zakończeniu prac Klient otrzymuje certyfikat wykonania usług."})]}),n.jsx("h2",{children:"\xa7 6. Płatności"}),(0,n.jsxs)("ol",{children:[n.jsx("li",{children:"Płatność następuje po zakończeniu i odbiorze prac."}),(0,n.jsxs)("li",{children:["Akceptowane formy płatności:",(0,n.jsxs)("ul",{children:[n.jsx("li",{children:"Got\xf3wka"}),n.jsx("li",{children:"Przelew bankowy"}),n.jsx("li",{children:"Karta płatnicza"}),n.jsx("li",{children:"BLIK"})]})]}),n.jsx("li",{children:"Dla klient\xf3w biznesowych możliwa jest płatność przelewem z terminem do 14 dni."}),n.jsx("li",{children:"W przypadku dużych zleceń możliwa jest zaliczka w wysokości 30%."})]}),n.jsx("h2",{children:"\xa7 7. Gwarancja i reklamacje"}),(0,n.jsxs)("ol",{children:[n.jsx("li",{children:"Usługodawca udziela 12-miesięcznej gwarancji na wykonane usługi."}),n.jsx("li",{children:"Reklamacje należy zgłaszać pisemnie w terminie 14 dni od wykonania usługi."}),n.jsx("li",{children:"Usługodawca zobowiązuje się rozpatrzyć reklamację w terminie 14 dni."}),n.jsx("li",{children:"W przypadku uzasadnionej reklamacji, Usługodawca wykona ponownie usługę bezpłatnie."})]}),n.jsx("h2",{children:"\xa7 8. Odpowiedzialność"}),(0,n.jsxs)("ol",{children:[n.jsx("li",{children:"Usługodawca posiada ubezpieczenie odpowiedzialności cywilnej."}),n.jsx("li",{children:"Odpowiedzialność Usługodawcy ograniczona jest do wysokości sumy ubezpieczenia."}),n.jsx("li",{children:"Usługodawca nie ponosi odpowiedzialności za szkody powstałe wskutek nieprawidłowego użytkowania obiektu przez Klienta."})]}),n.jsx("h2",{children:"\xa7 9. Siła wyższa"}),(0,n.jsxs)("ol",{children:[n.jsx("li",{children:"Usługodawca nie ponosi odpowiedzialności za niewykonanie lub nienależyte wykonanie zobowiązań z powodu siły wyższej."}),n.jsx("li",{children:"Za siłę wyższą uważa się zdarzenia niezależne od Usługodawcy, niemożliwe do przewidzenia i zapobieżenia."})]}),n.jsx("h2",{children:"\xa7 10. Ochrona danych osobowych"}),(0,n.jsxs)("ol",{children:[(0,n.jsxs)("li",{children:["Przetwarzanie danych osobowych odbywa się zgodnie z",n.jsx(s.default,{href:"/polityka-prywatnosci",className:"text-primary-600",children:"Polityką Prywatności"}),"."]}),n.jsx("li",{children:"Klient wyraża zgodę na przetwarzanie danych osobowych w celu realizacji umowy."})]}),n.jsx("h2",{children:"\xa7 11. Postanowienia końcowe"}),(0,n.jsxs)("ol",{children:[n.jsx("li",{children:"W sprawach nieuregulowanych niniejszym regulaminem stosuje się przepisy Kodeksu Cywilnego."}),n.jsx("li",{children:"Ewentualne spory rozstrzygane będą przez sąd właściwy dla siedziby Usługodawcy."}),n.jsx("li",{children:"Regulamin może być zmieniony przez Usługodawcę. O zmianach Klienci będą informowani na stronie internetowej."}),n.jsx("li",{children:"Regulamin wchodzi w życie z dniem 15 stycznia 2024 roku."})]})]})}),(0,n.jsxs)("div",{className:"mt-12 bg-primary-50 rounded-xl p-8",children:[n.jsx("h3",{className:"text-xl font-bold text-primary-900 mb-4",children:"Masz pytania dotyczące regulaminu?"}),n.jsx("p",{className:"text-primary-800 mb-6",children:"Skontaktuj się z nami - chętnie wyjaśnimy wszelkie wątpliwości dotyczące warunk\xf3w wsp\xf3łpracy."}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,n.jsxs)("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg transition-colors",children:[n.jsx(l.Z,{className:"w-4 h-4"}),n.jsx("span",{children:"<EMAIL>"})]}),(0,n.jsxs)("a",{href:"tel:+48123456789",className:"inline-flex items-center space-x-2 border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-6 py-3 rounded-lg transition-colors",children:[n.jsx(o.Z,{className:"w-4 h-4"}),n.jsx("span",{children:"+48 123 456 789"})]})]})]})]})]})}}};var i=require("../../webpack-runtime.js");i.C(e);var a=e=>i(i.s=e),n=i.X(0,[948,863,232],()=>a(9301));module.exports=n})();