"use strict";(()=>{var e={};e.id=703,e.ids=[703],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4192:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>c,patchFetch:()=>C,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>m,staticGenerationAsyncStorage:()=>x});var o={};r.r(o),r.d(o,{GET:()=>p});var a=r(9303),s=r(8716),n=r(3131),i=r(5661),u=r(707);async function p(){let e=await {rules:{userAgent:"*",allow:"/",disallow:["/admin/","/api/","/_next/"]},sitemap:"https://solvictus.pl/sitemap.xml"},t=(0,u.resolveRouteData)(e,"robots");return new i.NextResponse(t,{headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=0, must-revalidate"}})}let l=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"robots",bundlePath:"app/robots.txt/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Frobots.txt%2Froute&filePath=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp%5Crobots.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"export",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:x,serverHooks:m}=l,c="/robots.txt/route";function C(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:x})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[948,346],()=>r(4192));module.exports=o})();