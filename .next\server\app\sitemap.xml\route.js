"use strict";(()=>{var e={};e.id=717,e.ids=[717],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6674:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>x,patchFetch:()=>q,requestAsyncStorage:()=>g,routeModule:()=>h,serverHooks:()=>w,staticGenerationAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{default:()=>s});var i={};r.r(i),r.d(i,{GET:()=>m});var n=r(9303),o=r(8716),l=r(3131),u=r(5661);function s(){let e="https://solvictus.pl";return[{url:e,lastModified:new Date,changeFrequency:"weekly",priority:1},{url:`${e}/o-nas`,lastModified:new Date,changeFrequency:"monthly",priority:.8},{url:`${e}/uslugi`,lastModified:new Date,changeFrequency:"weekly",priority:.9},{url:`${e}/uslugi/sprzatanie-po-zgonach`,lastModified:new Date,changeFrequency:"monthly",priority:.8},{url:`${e}/uslugi/sprzatanie-po-pozarach`,lastModified:new Date,changeFrequency:"monthly",priority:.8},{url:`${e}/uslugi/sprzatanie-po-powodzi`,lastModified:new Date,changeFrequency:"monthly",priority:.8},{url:`${e}/uslugi/dezynfekcja-biologiczna`,lastModified:new Date,changeFrequency:"monthly",priority:.8},{url:`${e}/uslugi/ozonowanie`,lastModified:new Date,changeFrequency:"monthly",priority:.8},{url:`${e}/dla-firm`,lastModified:new Date,changeFrequency:"weekly",priority:.8},{url:`${e}/faq`,lastModified:new Date,changeFrequency:"monthly",priority:.7},{url:`${e}/kontakt`,lastModified:new Date,changeFrequency:"monthly",priority:.9},{url:`${e}/blog`,lastModified:new Date,changeFrequency:"weekly",priority:.6},{url:`${e}/opinie`,lastModified:new Date,changeFrequency:"weekly",priority:.6},{url:`${e}/polityka-prywatnosci`,lastModified:new Date,changeFrequency:"yearly",priority:.3},{url:`${e}/regulamin`,lastModified:new Date,changeFrequency:"yearly",priority:.3},{url:`${e}/rodo`,lastModified:new Date,changeFrequency:"yearly",priority:.3},{url:`${e}/cookies`,lastModified:new Date,changeFrequency:"yearly",priority:.3}]}var p=r(707);let d={...a},y=d.default,c=d.generateSitemaps;if("function"!=typeof y)throw Error('Default export is missing in "C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\sitemap.ts"');async function m(e,t){let r;let{__metadata_id__:a,...i}=t.params||{},n=c?await c():null;if(n&&null==(r=n.find(e=>{let t=e.id.toString();return(t+=".xml")===a})?.id))return new u.NextResponse("Not Found",{status:404});let o=await y({id:r}),l=(0,p.resolveRouteData)(o,"sitemap");return new u.NextResponse(l,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let h=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Fsitemap.xml%2Froute&filePath=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp%5Csitemap.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"export",userland:i}),{requestAsyncStorage:g,staticGenerationAsyncStorage:f,serverHooks:w}=h,x="/sitemap.xml/route";function q(){return(0,l.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:f})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[948,346],()=>r(6674));module.exports=a})();