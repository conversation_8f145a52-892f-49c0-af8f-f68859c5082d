/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/uslugi/page";
exports.ids = ["app/uslugi/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuslugi%2Fpage&page=%2Fuslugi%2Fpage&appPaths=%2Fuslugi%2Fpage&pagePath=private-next-app-dir%2Fuslugi%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuslugi%2Fpage&page=%2Fuslugi%2Fpage&appPaths=%2Fuslugi%2Fpage&pagePath=private-next-app-dir%2Fuslugi%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'uslugi',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/uslugi/page.tsx */ \"(rsc)/./src/app/uslugi/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/uslugi/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/uslugi/page\",\n        pathname: \"/uslugi\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuslugi%2Fpage&page=%2Fuslugi%2Fpage&appPaths=%2Fuslugi%2Fpage&pagePath=private-next-app-dir%2Fuslugi%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CChatbot.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CChatbot.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/script.js */ \"(ssr)/./node_modules/next/dist/client/script.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Chatbot.tsx */ \"(ssr)/./src/components/Chatbot.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Cscript.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CChatbot.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21hcml1JTVDJTVDRG93bmxvYWRzJTVDJTVDdGVzdDIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBa0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zb2x2aWN0dXMtd2Vic2l0ZS8/N2FmNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1hcml1XFxcXERvd25sb2Fkc1xcXFx0ZXN0MlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/Chatbot.tsx":
/*!************************************!*\
  !*** ./src/components/Chatbot.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Mail,MessageCircle,Phone,Send,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Mail,MessageCircle,Phone,Send,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Mail,MessageCircle,Phone,Send,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Mail,MessageCircle,Phone,Send,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Mail,MessageCircle,Phone,Send,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Mail,MessageCircle,Phone,Send,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Mail,MessageCircle,Phone,Send,User,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Chatbot = ()=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            text: \"Witaj! Jestem asystentem SOLVICTUS. Jak mogę Ci pom\\xf3c?\",\n            isBot: true,\n            timestamp: new Date(),\n            options: [\n                \"Potrzebuję natychmiastowej pomocy\",\n                \"Chcę poznać nasze usługi\",\n                \"Pytanie o cennik\",\n                \"Kontakt z ekspertem\"\n            ]\n        }\n    ]);\n    const [inputText, setInputText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const botResponses = {\n        \"potrzebuję natychmiastowej pomocy\": {\n            text: \"Rozumiem, że sytuacja jest pilna. Nasz zesp\\xf3ł jest dostępny 24/7. Zadzwoń natychmiast pod numer +48 123 456 789 lub wybierz rodzaj sytuacji:\",\n            options: [\n                \"Zgon w mieszkaniu\",\n                \"Pożar\",\n                \"Zalanie/pow\\xf3dź\",\n                \"Skażenie biologiczne\",\n                \"Zadzwoń teraz\"\n            ]\n        },\n        \"chcę poznać nasze usługi\": {\n            text: \"Oferujemy kompleksowe usługi specjalistycznego sprzątania. Wybierz kategorię, kt\\xf3ra Cię interesuje:\",\n            options: [\n                \"Sprzątanie po zgonach\",\n                \"Sprzątanie po pożarach\",\n                \"Usuwanie skutk\\xf3w powodzi\",\n                \"Dezynfekcja biologiczna\",\n                \"Ozonowanie\",\n                \"Usługi dla firm\"\n            ]\n        },\n        \"pytanie o cennik\": {\n            text: \"Nasze ceny są konkurencyjne i zależą od wielu czynnik\\xf3w. Podstawowe zakresy cenowe:\",\n            options: [\n                \"Sprzątanie po zgonach: 2000-8000 zł\",\n                \"Po pożarach: 3000-15000 zł\",\n                \"Po powodziach: 1500-10000 zł\",\n                \"Dezynfekcja: 800-5000 zł\",\n                \"Bezpłatna wycena\"\n            ]\n        },\n        \"kontakt z ekspertem\": {\n            text: \"Możesz skontaktować się z naszymi ekspertami na kilka sposob\\xf3w:\",\n            options: [\n                \"Telefon: +48 123 456 789\",\n                \"Email: <EMAIL>\",\n                \"Formularz kontaktowy\",\n                \"Um\\xf3w bezpłatną konsultację\"\n            ]\n        },\n        \"zgon w mieszkaniu\": {\n            text: \"To bardzo trudna sytuacja. Nasz zesp\\xf3ł specjalizuje się w dyskretnym sprzątaniu po zgonach. Oferujemy:\\n\\n• Dezynfekcję biologiczną\\n• Usuwanie zapach\\xf3w\\n• Ozonowanie\\n• Pełną dyskrecję\\n\\nZadzwoń natychmiast: +48 123 456 789\",\n            options: [\n                \"Zadzwoń teraz\",\n                \"Więcej informacji\",\n                \"Cennik\"\n            ]\n        },\n        \"pożar\": {\n            text: \"Pomożemy usunąć skutki pożaru:\\n\\n• Usuwanie sadzy\\n• Neutralizacja zapach\\xf3w dymu\\n• Czyszczenie instalacji\\n• Ocena szk\\xf3d\\n\\nSkontaktuj się z nami: +48 123 456 789\",\n            options: [\n                \"Zadzwoń teraz\",\n                \"Procedura sprzątania\",\n                \"Wsp\\xf3łpraca z ubezpieczycielami\"\n            ]\n        },\n        \"zalanie/pow\\xf3dź\": {\n            text: \"Specjalizujemy się w usuwaniu skutk\\xf3w zalań:\\n\\n• Osuszanie pomieszczeń\\n• Zapobieganie pleśni\\n• Dezynfekcja\\n• Monitoring wilgotności\\n\\nKontakt: +48 123 456 789\",\n            options: [\n                \"Zadzwoń teraz\",\n                \"Proces osuszania\",\n                \"Cennik\"\n            ]\n        },\n        \"skażenie biologiczne\": {\n            text: \"Oferujemy profesjonalną dezynfekcję biologiczną:\\n\\n• Eliminacja patogen\\xf3w\\n• Certyfikowane środki\\n• Zgodność z normami\\n• Dokumentacja\\n\\nPilny kontakt: +48 123 456 789\",\n            options: [\n                \"Zadzwoń teraz\",\n                \"Rodzaje dezynfekcji\",\n                \"Certyfikaty\"\n            ]\n        }\n    };\n    const addMessage = (text, isBot, options)=>{\n        const newMessage = {\n            id: Date.now(),\n            text,\n            isBot,\n            timestamp: new Date(),\n            options\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n    };\n    const handleSendMessage = (text)=>{\n        if (!text.trim()) return;\n        // Add user message\n        addMessage(text, false);\n        setInputText(\"\");\n        setIsTyping(true);\n        // Simulate bot typing delay\n        setTimeout(()=>{\n            setIsTyping(false);\n            const lowerText = text.toLowerCase();\n            let response = botResponses[lowerText];\n            if (!response) {\n                // Try to find partial matches\n                const keys = Object.keys(botResponses);\n                const matchedKey = keys.find((key)=>lowerText.includes(key.toLowerCase()) || key.toLowerCase().includes(lowerText));\n                if (matchedKey) {\n                    response = botResponses[matchedKey];\n                }\n            }\n            if (response) {\n                addMessage(response.text, true, response.options);\n            } else {\n                // Default response\n                addMessage(\"Dziękuję za wiadomość. Jeśli potrzebujesz natychmiastowej pomocy, zadzwoń pod numer +48 123 456 789. W innych sprawach nasz zesp\\xf3ł odpowie w ciągu 24 godzin.\", true, [\n                    \"Zadzwoń teraz\",\n                    \"Wyślij email\",\n                    \"Formularz kontaktowy\"\n                ]);\n            }\n        }, 1000 + Math.random() * 1000);\n    };\n    const handleOptionClick = (option)=>{\n        if (option === \"Zadzwoń teraz\") {\n            window.open(\"tel:+48123456789\", \"_self\");\n            return;\n        }\n        if (option.includes(\"Email:\") || option === \"Wyślij email\") {\n            window.open(\"mailto:<EMAIL>\", \"_self\");\n            return;\n        }\n        if (option === \"Formularz kontaktowy\") {\n            window.open(\"/kontakt\", \"_blank\");\n            return;\n        }\n        if (option === \"Bezpłatna wycena\" || option === \"Um\\xf3w bezpłatną konsultację\") {\n            window.open(\"/kontakt\", \"_blank\");\n            return;\n        }\n        handleSendMessage(option);\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        handleSendMessage(inputText);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(true),\n                className: `fixed bottom-6 right-6 w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40 ${isOpen ? \"scale-0\" : \"scale-100\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed bottom-6 right-6 w-80 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 z-50 flex flex-col transition-all duration-300 ${isOpen ? \"scale-100 opacity-100\" : \"scale-0 opacity-0\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-primary-600 text-white p-4 rounded-t-lg flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-white/20 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold\",\n                                                children: \"Asystent SOLVICTUS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-primary-100\",\n                                                children: \"Online • Odpowiada natychmiast\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(false),\n                                className: \"p-1 hover:bg-white/20 rounded transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                        children: [\n                            messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `flex ${message.isBot ? \"justify-start\" : \"justify-end\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `max-w-xs ${message.isBot ? \"order-2\" : \"order-1\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `px-3 py-2 rounded-lg text-sm ${message.isBot ? \"bg-gray-100 text-gray-800\" : \"bg-primary-600 text-white\"}`,\n                                                    children: message.text\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                message.options && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 space-y-1\",\n                                                    children: message.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleOptionClick(option),\n                                                            className: \"block w-full text-left px-3 py-2 text-xs bg-white border border-gray-200 rounded hover:bg-gray-50 transition-colors\",\n                                                            children: option\n                                                        }, index, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `text-xs text-gray-500 mt-1 ${message.isBot ? \"text-left\" : \"text-right\"}`,\n                                                    children: message.timestamp.toLocaleTimeString(\"pl-PL\", {\n                                                        hour: \"2-digit\",\n                                                        minute: \"2-digit\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${message.isBot ? \"bg-gray-300 text-gray-600 order-1 mr-2\" : \"bg-primary-600 text-white order-2 ml-2\"}`,\n                                            children: message.isBot ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 34\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 64\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, message.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined)),\n                            isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-6 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center text-xs font-bold mr-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-100 px-3 py-2 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.1s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                                    style: {\n                                                        animationDelay: \"0.2s\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: messagesEndRef\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"p-4 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        value: inputText,\n                                        onChange: (e)=>setInputText(e.target.value),\n                                        placeholder: \"Napisz wiadomość...\",\n                                        className: \"flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: !inputText.trim(),\n                                        className: \"bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white p-2 rounded-lg transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>window.open(\"tel:+48123456789\", \"_self\"),\n                                        className: \"flex items-center space-x-1 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Pilne\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>window.open(\"/kontakt\", \"_blank\"),\n                                        className: \"flex items-center space-x-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Mail_MessageCircle_Phone_Send_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-3 h-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Kontakt\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Chatbot.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Chatbot);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Chatbot.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageSwitcher */ \"(ssr)/./src/components/LanguageSwitcher.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [locale, setLocale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pl\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 50);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navigation = [\n        {\n            name: \"Strona gł\\xf3wna\",\n            href: \"/\"\n        },\n        {\n            name: \"O nas\",\n            href: \"/o-nas\"\n        },\n        {\n            name: \"Usługi\",\n            href: \"/uslugi\"\n        },\n        {\n            name: \"Dla firm\",\n            href: \"/dla-firm\"\n        },\n        {\n            name: \"FAQ\",\n            href: \"/faq\"\n        },\n        {\n            name: \"Kontakt\",\n            href: \"/kontakt\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-900 text-white py-2 px-4 text-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto flex flex-col sm:flex-row justify-between items-center space-y-1 sm:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"24/7 Linia pomocy: +48 123 456 789\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Certyfikowana dezynfekcja\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: `sticky top-0 z-50 transition-all duration-300 ${isScrolled ? \"bg-white/95 backdrop-blur-md shadow-lg\" : \"bg-white\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-900 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold gradient-text\",\n                                                    children: \"SOLVICTUS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 -mt-1\",\n                                                    children: \"Pomagamy po tragedii\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:flex items-center space-x-8\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            className: \"text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200 relative group\",\n                                            children: [\n                                                item.name,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            currentLocale: locale,\n                                            onLocaleChange: setLocale\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/kontakt\",\n                                            className: \"bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 hover:shadow-lg\",\n                                            children: \"Zgłoś sytuację\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"lg:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                currentLocale: locale,\n                                                onLocaleChange: setLocale\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                            className: \"p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100\",\n                                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 31\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 59\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined),\n                    isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden bg-white border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 space-y-1\",\n                            children: [\n                                navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: item.href,\n                                        className: \"block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md font-medium\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: item.name\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-2 border-t border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/kontakt\",\n                                        className: \"block w-full text-center bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Zgłoś sytuację\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageSwitcher.tsx":
/*!*********************************************!*\
  !*** ./src/components/LanguageSwitcher.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./src/lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst LanguageSwitcher = ({ currentLocale, onLocaleChange })=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const languages = {\n        pl: {\n            name: \"Polski\",\n            flag: \"\\uD83C\\uDDF5\\uD83C\\uDDF1\"\n        },\n        en: {\n            name: \"English\",\n            flag: \"\\uD83C\\uDDEC\\uD83C\\uDDE7\"\n        }\n    };\n    const handleLocaleChange = (locale)=>{\n        onLocaleChange(locale);\n        setIsOpen(false);\n        // Store preference in localStorage (only in browser)\n        if (false) {}\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-primary-600 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: [\n                            languages[currentLocale].flag,\n                            \" \",\n                            languages[currentLocale].name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: `w-4 h-4 transition-transform ${isOpen ? \"rotate-180\" : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-10\",\n                        onClick: ()=>setIsOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2\",\n                            children: _lib_i18n__WEBPACK_IMPORTED_MODULE_2__.locales.map((locale)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleLocaleChange(locale),\n                                    className: `w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors ${currentLocale === locale ? \"bg-primary-50 text-primary-600\" : \"text-gray-700\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: languages[locale].flag\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: languages[locale].name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        currentLocale === locale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto text-primary-600\",\n                                            children: \"✓\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, locale, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageSwitcher);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/i18n.ts":
/*!*************************!*\
  !*** ./src/lib/i18n.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   t: () => (/* binding */ t),\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\nconst defaultLocale = \"pl\";\nconst locales = [\n    \"pl\",\n    \"en\"\n];\nconst translations = {\n    pl: {\n        // Navigation\n        nav: {\n            home: \"Strona gł\\xf3wna\",\n            about: \"O nas\",\n            services: \"Usługi\",\n            business: \"Dla firm\",\n            gallery: \"Galeria\",\n            blog: \"Blog\",\n            reviews: \"Opinie\",\n            pricing: \"Cennik\",\n            faq: \"FAQ\",\n            contact: \"Kontakt\"\n        },\n        // Common\n        common: {\n            readMore: \"Czytaj więcej\",\n            learnMore: \"Dowiedz się więcej\",\n            contactUs: \"Skontaktuj się z nami\",\n            callNow: \"Zadzwoń teraz\",\n            freeQuote: \"Bezpłatna wycena\",\n            emergency: \"Sytuacja kryzysowa\",\n            available247: \"Dostępni 24/7\",\n            phone: \"Telefon\",\n            email: \"Email\",\n            address: \"Adres\",\n            loading: \"Ładowanie...\",\n            submit: \"Wyślij\",\n            cancel: \"Anuluj\",\n            close: \"Zamknij\",\n            next: \"Następny\",\n            previous: \"Poprzedni\",\n            showMore: \"Pokaż więcej\",\n            showLess: \"Pokaż mniej\"\n        },\n        // Hero section\n        hero: {\n            title: \"Profesjonalne sprzątanie po tragedii\",\n            subtitle: \"Certyfikowane usługi sprzątania i dezynfekcji po traumatycznych wydarzeniach. Pomagamy przywr\\xf3cić bezpieczeństwo i higienę z pełną dyskrecją.\",\n            emergencyLine: \"Linia kryzysowa 24/7\",\n            reportSituation: \"Zgłoś sytuację\",\n            ourServices: \"Nasze usługi\"\n        },\n        // Services\n        services: {\n            deathCleanup: \"Sprzątanie po zgonach\",\n            fireCleanup: \"Sprzątanie po pożarach\",\n            floodCleanup: \"Usuwanie skutk\\xf3w powodzi\",\n            biologicalDisinfection: \"Dezynfekcja biologiczna\",\n            ozonetreatment: \"Ozonowanie pomieszczeń\",\n            businessServices: \"Usługi dla firm\"\n        },\n        // Footer\n        footer: {\n            tagline: \"Pomagamy po tragedii\",\n            quickLinks: \"Szybkie linki\",\n            services: \"Usługi\",\n            company: \"Firma\",\n            contact: \"Kontakt\",\n            legal: \"Informacje prawne\",\n            privacy: \"Polityka prywatności\",\n            terms: \"Regulamin\",\n            cookies: \"Polityka cookies\",\n            rodo: \"RODO\",\n            copyright: \"Wszystkie prawa zastrzeżone.\",\n            emergencyContact: \"Kontakt kryzysowy\",\n            businessHours: \"Godziny pracy biura\",\n            businessHoursTime: \"Pn-Pt: 8:00-18:00\",\n            emergencyAvailable: \"Interwencje: 24/7\"\n        }\n    },\n    en: {\n        // Navigation\n        nav: {\n            home: \"Home\",\n            about: \"About Us\",\n            services: \"Services\",\n            business: \"For Business\",\n            gallery: \"Gallery\",\n            blog: \"Blog\",\n            reviews: \"Reviews\",\n            pricing: \"Pricing\",\n            faq: \"FAQ\",\n            contact: \"Contact\"\n        },\n        // Common\n        common: {\n            readMore: \"Read more\",\n            learnMore: \"Learn more\",\n            contactUs: \"Contact us\",\n            callNow: \"Call now\",\n            freeQuote: \"Free quote\",\n            emergency: \"Emergency situation\",\n            available247: \"Available 24/7\",\n            phone: \"Phone\",\n            email: \"Email\",\n            address: \"Address\",\n            loading: \"Loading...\",\n            submit: \"Submit\",\n            cancel: \"Cancel\",\n            close: \"Close\",\n            next: \"Next\",\n            previous: \"Previous\",\n            showMore: \"Show more\",\n            showLess: \"Show less\"\n        },\n        // Hero section\n        hero: {\n            title: \"Professional trauma cleaning services\",\n            subtitle: \"Certified cleaning and disinfection services after traumatic events. We help restore safety and hygiene with complete discretion.\",\n            emergencyLine: \"24/7 Emergency line\",\n            reportSituation: \"Report situation\",\n            ourServices: \"Our services\"\n        },\n        // Services\n        services: {\n            deathCleanup: \"Death cleanup\",\n            fireCleanup: \"Fire damage cleanup\",\n            floodCleanup: \"Flood damage restoration\",\n            biologicalDisinfection: \"Biological disinfection\",\n            ozonetreatment: \"Ozone treatment\",\n            businessServices: \"Business services\"\n        },\n        // Footer\n        footer: {\n            tagline: \"Helping after tragedy\",\n            quickLinks: \"Quick links\",\n            services: \"Services\",\n            company: \"Company\",\n            contact: \"Contact\",\n            legal: \"Legal information\",\n            privacy: \"Privacy policy\",\n            terms: \"Terms of service\",\n            cookies: \"Cookie policy\",\n            rodo: \"GDPR\",\n            copyright: \"All rights reserved.\",\n            emergencyContact: \"Emergency contact\",\n            businessHours: \"Office hours\",\n            businessHoursTime: \"Mon-Fri: 8:00-18:00\",\n            emergencyAvailable: \"Interventions: 24/7\"\n        }\n    }\n};\nfunction getTranslation(locale, key) {\n    const keys = key.split(\".\");\n    let value = translations[locale];\n    for (const k of keys){\n        value = value?.[k];\n    }\n    return value || key;\n}\nfunction t(key, locale = defaultLocale) {\n    return getTranslation(locale, key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8a9929394fc4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc29sdmljdHVzLXdlYnNpdGUvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzMwZDIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4YTk5MjkzOTRmYzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_SEOSchema__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SEOSchema */ \"(rsc)/./src/components/SEOSchema.tsx\");\n/* harmony import */ var _components_Chatbot__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Chatbot */ \"(rsc)/./src/components/Chatbot.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"SOLVICTUS - Profesjonalne sprzątanie po tragedii | Certyfikowana dezynfekcja\",\n    description: \"SOLVICTUS oferuje profesjonalne, certyfikowane sprzątanie i dezynfekcję po zgonach, pożarach, powodziach i skażeniach biologicznych. Zaufanie. Cisza. Skuteczność.\",\n    keywords: \"sprzątanie po zgonach, dezynfekcja po śmierci, sprzątanie po tragedii, ozonowanie, usuwanie zapach\\xf3w, sprzątanie po pożarze, sprzątanie po powodzi, dezynfekcja biologiczna\",\n    authors: [\n        {\n            name: \"SOLVICTUS\"\n        }\n    ],\n    creator: \"SOLVICTUS\",\n    publisher: \"SOLVICTUS\",\n    robots: \"index, follow\",\n    openGraph: {\n        type: \"website\",\n        locale: \"pl_PL\",\n        url: \"https://solvictus.pl\",\n        title: \"SOLVICTUS - Profesjonalne sprzątanie po tragedii\",\n        description: \"Certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach. Pomagamy po tragedii.\",\n        siteName: \"SOLVICTUS\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SOLVICTUS - Profesjonalne sprzątanie po tragedii\",\n        description: \"Certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach.\"\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    themeColor: \"#0A2144\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pl\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_6___default().variable)} smooth-scroll`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_6___default().className)} antialiased bg-gray-50`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SEOSchema__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        type: \"organization\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chatbot__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/uslugi/page.tsx":
/*!*********************************!*\
  !*** ./src/app/uslugi/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ServicesPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building2,Droplets,Flame,Heart,Phone,Shield,Wind,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building2,Droplets,Flame,Heart,Phone,Shield,Wind,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/flame.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building2,Droplets,Flame,Heart,Phone,Shield,Wind,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/droplets.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building2,Droplets,Flame,Heart,Phone,Shield,Wind,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building2,Droplets,Flame,Heart,Phone,Shield,Wind,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/wind.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building2,Droplets,Flame,Heart,Phone,Shield,Wind,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building2,Droplets,Flame,Heart,Phone,Shield,Wind,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building2,Droplets,Flame,Heart,Phone,Shield,Wind,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Building2,Droplets,Flame,Heart,Phone,Shield,Wind,Zap!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n\n\n\nconst metadata = {\n    title: \"Usługi - SOLVICTUS | Profesjonalne sprzątanie po tragedii\",\n    description: \"Kompleksowe usługi sprzątania i dezynfekcji: po zgonach, pożarach, powodziach, skażeniach biologicznych. Certyfikowani specjaliści, dostępni 24/7.\",\n    keywords: \"usługi sprzątania, dezynfekcja po zgonie, sprzątanie po pożarze, usuwanie skutk\\xf3w powodzi, ozonowanie, dezynfekcja biologiczna\"\n};\nconst services = [\n    {\n        icon: _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Sprzątanie po zgonach\",\n        description: \"Profesjonalne sprzątanie i dezynfekcja po śmierci naturalnej lub nienaturalnej z pełnym zachowaniem dyskrecji\",\n        features: [\n            \"Dezynfekcja biologiczna powierzchni\",\n            \"Usuwanie zapach\\xf3w metodą ozonowania\",\n            \"Przywracanie higieny pomieszczeń\",\n            \"Utylizacja skażonych materiał\\xf3w\",\n            \"Wsparcie psychologiczne dla rodziny\"\n        ],\n        process: [\n            \"Ocena sytuacji i planowanie działań\",\n            \"Zabezpieczenie miejsca pracy\",\n            \"Usuwanie skażonych materiał\\xf3w\",\n            \"Dezynfekcja i neutralizacja zapach\\xf3w\",\n            \"Kontrola jakości i dokumentacja\"\n        ],\n        href: \"/uslugi/sprzatanie-po-zgonach\",\n        urgency: \"Natychmiastowa interwencja\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Sprzątanie po pożarach\",\n        description: \"Kompleksowe usuwanie skutk\\xf3w pożar\\xf3w, sadzy, dymu i przywracanie pomieszczeń do stanu użytkowego\",\n        features: [\n            \"Usuwanie sadzy i osad\\xf3w\",\n            \"Neutralizacja zapach\\xf3w dymu\",\n            \"Czyszczenie instalacji wentylacyjnych\",\n            \"Renowacja powierzchni\",\n            \"Ocena szk\\xf3d i dokumentacja\"\n        ],\n        process: [\n            \"Inspekcja i ocena szk\\xf3d\",\n            \"Zabezpieczenie przed dalszymi uszkodzeniami\",\n            \"Usuwanie sadzy i zanieczyszczeń\",\n            \"Dezynfekcja i odgrzybianie\",\n            \"Przywracanie stanu pierwotnego\"\n        ],\n        href: \"/uslugi/sprzatanie-po-pozarach\",\n        urgency: \"Interwencja w 24h\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Usuwanie skutk\\xf3w powodzi\",\n        description: \"Profesjonalne osuszanie, dezynfekcja i przywracanie pomieszczeń po zalaniach i powodziach\",\n        features: [\n            \"Osuszanie pomieszczeń\",\n            \"Zapobieganie rozwojowi pleśni\",\n            \"Dezynfekcja powierzchni\",\n            \"Usuwanie wilgoci ze ścian\",\n            \"Monitoring poziomu wilgotności\"\n        ],\n        process: [\n            \"Usuwanie stojącej wody\",\n            \"Osuszanie za pomocą specjalistycznego sprzętu\",\n            \"Dezynfekcja przeciwgrzybicza\",\n            \"Monitoring wilgotności\",\n            \"Przywracanie normalnych warunk\\xf3w\"\n        ],\n        href: \"/uslugi/sprzatanie-po-powodzi\",\n        urgency: \"Interwencja w 12h\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Dezynfekcja biologiczna\",\n        description: \"Eliminacja patogen\\xf3w, wirus\\xf3w, bakterii i innych zagrożeń biologicznych z pomieszczeń\",\n        features: [\n            \"Dezynfekcja wirusowa i bakteryjna\",\n            \"Eliminacja patogen\\xf3w\",\n            \"Bezpieczne środki dezynfekcyjne\",\n            \"Certyfikowane procedury\",\n            \"Dokumentacja procesu\"\n        ],\n        process: [\n            \"Identyfikacja zagrożeń biologicznych\",\n            \"Dob\\xf3r odpowiednich środk\\xf3w\",\n            \"Przeprowadzenie dezynfekcji\",\n            \"Kontrola skuteczności\",\n            \"Wydanie certyfikatu\"\n        ],\n        href: \"/uslugi/dezynfekcja-biologiczna\",\n        urgency: \"Według potrzeb\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        title: \"Ozonowanie pomieszczeń\",\n        description: \"Zaawansowane oczyszczanie powietrza i powierzchni metodą ozonowania - ekologicznie i skutecznie\",\n        features: [\n            \"Usuwanie nieprzyjemnych zapach\\xf3w\",\n            \"Dezynfekcja powietrza\",\n            \"Eliminacja alergen\\xf3w\",\n            \"Metoda ekologiczna\",\n            \"Brak pozostałości chemicznych\"\n        ],\n        process: [\n            \"Przygotowanie pomieszczenia\",\n            \"Ustawienie generator\\xf3w ozonu\",\n            \"Proces ozonowania\",\n            \"Wentylacja i neutralizacja\",\n            \"Kontrola jakości powietrza\"\n        ],\n        href: \"/uslugi/ozonowanie\",\n        urgency: \"Planowane\"\n    },\n    {\n        icon: _barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        title: \"Usługi dla firm\",\n        description: \"Specjalistyczne rozwiązania dla przedsiębiorstw, instytucji i obiekt\\xf3w komercyjnych\",\n        features: [\n            \"Kontrakty długoterminowe\",\n            \"Szybka reakcja 24/7\",\n            \"Certyfikaty dla ubezpieczycieli\",\n            \"Wsparcie prawne\",\n            \"Indywidualne rozwiązania\"\n        ],\n        process: [\n            \"Analiza potrzeb klienta\",\n            \"Przygotowanie oferty\",\n            \"Podpisanie umowy\",\n            \"Realizacja usług\",\n            \"Monitoring i raportowanie\"\n        ],\n        href: \"/dla-firm\",\n        urgency: \"Według umowy\"\n    }\n];\nfunction ServicesPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pt-24\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-5 h-5 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-sm font-medium\",\n                                    children: \"Nasze specjalizacje\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight\",\n                            children: \"Nasze usługi\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl md:text-2xl text-gray-200 mb-8 max-w-4xl mx-auto leading-relaxed\",\n                            children: \"Oferujemy kompleksowe rozwiązania w zakresie specjalistycznego sprzątania i dezynfekcji. Każda sytuacja wymaga indywidualnego podejścia i najwyższych standard\\xf3w bezpieczeństwa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:+48123456789\",\n                                    className: \"bg-white text-primary-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Zadzwoń teraz\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/kontakt\",\n                                    className: \"border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-900 transition-colors\",\n                                    children: \"Bezpłatna wycena\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12\",\n                        children: services.map((service, index)=>{\n                            const IconComponent = service.icon;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-50 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 card-hover\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-2xl font-bold text-gray-900\",\n                                                            children: service.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded-full font-medium\",\n                                                            children: service.urgency\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-6 leading-relaxed\",\n                                                    children: service.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-gray-900 mb-3\",\n                                                                    children: \"Co obejmuje:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: service.features.slice(0, 3).map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-1.5 h-1.5 bg-primary-500 rounded-full flex-shrink-0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                                                    lineNumber: 224,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: feature\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                                                    lineNumber: 225,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, featureIndex, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-gray-900 mb-3\",\n                                                                    children: \"Proces realizacji:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: service.process.slice(0, 3).map((step, stepIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                            className: \"flex items-center space-x-2 text-sm text-gray-600\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-4 h-4 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0\",\n                                                                                    children: stepIndex + 1\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                                                    lineNumber: 236,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: step\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                                                    lineNumber: 239,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, stepIndex, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                                            lineNumber: 235,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: service.href,\n                                                    className: \"inline-flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Dowiedz się więcej\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"w-4 h-4 transition-transform group-hover:translate-x-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 19\n                                }, this)\n                            }, index, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-red-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-red-600 to-red-700 rounded-2xl p-8 md:p-12 text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold mb-4\",\n                                children: \"Sytuacja kryzysowa?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-100 mb-8 max-w-2xl mx-auto\",\n                                children: \"Nasz zesp\\xf3ł jest dostępny 24/7. Zadzwoń teraz, a otrzymasz natychmiastową pomoc i profesjonalne wsparcie w trudnej sytuacji.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"tel:+48123456789\",\n                                className: \"inline-flex items-center space-x-2 bg-white text-red-600 px-8 py-4 rounded-lg font-bold text-xl hover:bg-gray-100 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Building2_Droplets_Flame_Heart_Phone_Shield_Wind_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"+48 123 456 789\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\page.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/uslugi/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Chatbot.tsx":
/*!************************************!*\
  !*** ./src/components/Chatbot.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\test2\src\components\Chatbot.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n\n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const services = [\n        \"Sprzątanie po zgonach\",\n        \"Dezynfekcja po śmierci\",\n        \"Sprzątanie po pożarach\",\n        \"Usuwanie skutk\\xf3w powodzi\",\n        \"Ozonowanie pomieszczeń\",\n        \"Usuwanie zapach\\xf3w\"\n    ];\n    const quickLinks = [\n        {\n            name: \"O nas\",\n            href: \"/o-nas\"\n        },\n        {\n            name: \"Usługi\",\n            href: \"/uslugi\"\n        },\n        {\n            name: \"Dla firm\",\n            href: \"/dla-firm\"\n        },\n        {\n            name: \"FAQ\",\n            href: \"/faq\"\n        },\n        {\n            name: \"Kontakt\",\n            href: \"/kontakt\"\n        }\n    ];\n    const additionalLinks = [\n        {\n            name: \"Galeria realizacji\",\n            href: \"/galeria\"\n        },\n        {\n            name: \"Blog i poradniki\",\n            href: \"/blog\"\n        },\n        {\n            name: \"Opinie klient\\xf3w\",\n            href: \"/opinie\"\n        },\n        {\n            name: \"Cennik usług\",\n            href: \"/cennik\"\n        }\n    ];\n    const legalLinks = [\n        {\n            name: \"Polityka prywatności\",\n            href: \"/polityka-prywatnosci\"\n        },\n        {\n            name: \"Regulamin\",\n            href: \"/regulamin\"\n        },\n        {\n            name: \"RODO\",\n            href: \"/rodo\"\n        },\n        {\n            name: \"Cookies\",\n            href: \"/cookies\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-primary-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-white rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-primary-900\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 46,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"SOLVICTUS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-300\",\n                                                    children: \"Pomagamy po tragedii\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm leading-relaxed\",\n                                    children: \"Profesjonalne, certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach. Działamy z dyskrecją, empatią i najwyższą skutecznością.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-300 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-300 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-300 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 65,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Nasze usługi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/uslugi\",\n                                                className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                                children: service\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Szybkie linki\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Więcej\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: additionalLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Kontakt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"24/7 Dostępność\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: \"Całodobowa linia pomocy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"+48 123 456 789\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: \"Natychmiastowa pomoc\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: \"Odpowiedź w 24h\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Warszawa, Polska\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: \"Działamy w całym kraju\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-primary-800 mt-8 pt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        currentYear,\n                                        \" SOLVICTUS. Wszystkie prawa zastrzeżone.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center md:justify-end space-x-4 text-xs\",\n                                    children: legalLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: link.href,\n                                            className: \"text-gray-300 hover:text-white transition-colors\",\n                                            children: link.name\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 text-xs text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Certyfikowane przez Państwowy Zakład Higieny | Licencja nr: PZH/2024/001\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\test2\src\components\Header.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/SEOSchema.tsx":
/*!**************************************!*\
  !*** ./src/components/SEOSchema.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n\n\nconst SEOSchema = ({ type = \"organization\", title, description, url })=>{\n    const organizationSchema = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Organization\",\n        \"name\": \"SOLVICTUS\",\n        \"description\": \"Profesjonalne, certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach\",\n        \"url\": \"https://solvictus.pl\",\n        \"logo\": \"https://solvictus.pl/logo.png\",\n        \"contactPoint\": {\n            \"@type\": \"ContactPoint\",\n            \"telephone\": \"+**************\",\n            \"contactType\": \"emergency\",\n            \"availableLanguage\": \"Polish\",\n            \"hoursAvailable\": \"24/7\"\n        },\n        \"address\": {\n            \"@type\": \"PostalAddress\",\n            \"streetAddress\": \"ul. Przykładowa 123\",\n            \"addressLocality\": \"Warszawa\",\n            \"postalCode\": \"00-001\",\n            \"addressCountry\": \"PL\"\n        },\n        \"areaServed\": {\n            \"@type\": \"Country\",\n            \"name\": \"Poland\"\n        },\n        \"serviceType\": [\n            \"Sprzątanie po zgonach\",\n            \"Dezynfekcja po śmierci\",\n            \"Sprzątanie po pożarach\",\n            \"Usuwanie skutk\\xf3w powodzi\",\n            \"Ozonowanie pomieszczeń\",\n            \"Dezynfekcja biologiczna\"\n        ],\n        \"hasCredential\": [\n            {\n                \"@type\": \"EducationalOccupationalCredential\",\n                \"name\": \"Certyfikat PZH\",\n                \"credentialCategory\": \"Dezynfekcja\"\n            },\n            {\n                \"@type\": \"EducationalOccupationalCredential\",\n                \"name\": \"ISO 9001:2015\",\n                \"credentialCategory\": \"Zarządzanie jakością\"\n            }\n        ]\n    };\n    const serviceSchema = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Service\",\n        \"name\": title || \"Profesjonalne sprzątanie po tragedii\",\n        \"description\": description || \"Certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach\",\n        \"provider\": {\n            \"@type\": \"Organization\",\n            \"name\": \"SOLVICTUS\",\n            \"url\": \"https://solvictus.pl\"\n        },\n        \"areaServed\": {\n            \"@type\": \"Country\",\n            \"name\": \"Poland\"\n        },\n        \"availableChannel\": {\n            \"@type\": \"ServiceChannel\",\n            \"servicePhone\": \"+**************\",\n            \"serviceUrl\": \"https://solvictus.pl/kontakt\"\n        },\n        \"hoursAvailable\": \"24/7\",\n        \"category\": \"Cleaning Services\"\n    };\n    const localBusinessSchema = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"LocalBusiness\",\n        \"name\": \"SOLVICTUS\",\n        \"image\": \"https://solvictus.pl/logo.png\",\n        \"telephone\": \"+**************\",\n        \"email\": \"<EMAIL>\",\n        \"address\": {\n            \"@type\": \"PostalAddress\",\n            \"streetAddress\": \"ul. Przykładowa 123\",\n            \"addressLocality\": \"Warszawa\",\n            \"postalCode\": \"00-001\",\n            \"addressCountry\": \"PL\"\n        },\n        \"geo\": {\n            \"@type\": \"GeoCoordinates\",\n            \"latitude\": 52.2297,\n            \"longitude\": 21.0122\n        },\n        \"url\": \"https://solvictus.pl\",\n        \"openingHours\": \"Mo-Su 00:00-23:59\",\n        \"priceRange\": \"$$\",\n        \"aggregateRating\": {\n            \"@type\": \"AggregateRating\",\n            \"ratingValue\": \"4.9\",\n            \"reviewCount\": \"127\"\n        }\n    };\n    const getSchema = ()=>{\n        switch(type){\n            case \"service\":\n                return serviceSchema;\n            case \"organization\":\n                return [\n                    organizationSchema,\n                    localBusinessSchema\n                ];\n            default:\n                return organizationSchema;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"structured-data\",\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(getSchema())\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\SEOSchema.tsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SEOSchema);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/SEOSchema.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuslugi%2Fpage&page=%2Fuslugi%2Fpage&appPaths=%2Fuslugi%2Fpage&pagePath=private-next-app-dir%2Fuslugi%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();