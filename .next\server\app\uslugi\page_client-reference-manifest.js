globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/uslugi/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/components/CertificationsSection.tsx":{"*":{"id":"(ssr)/./src/components/CertificationsSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/EmergencyContact.tsx":{"*":{"id":"(ssr)/./src/components/EmergencyContact.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HeroSection.tsx":{"*":{"id":"(ssr)/./src/components/HeroSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ServicesOverview.tsx":{"*":{"id":"(ssr)/./src/components/ServicesOverview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/TestimonialsPreview.tsx":{"*":{"id":"(ssr)/./src/components/TestimonialsPreview.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/WhyChooseUs.tsx":{"*":{"id":"(ssr)/./src/components/WhyChooseUs.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ChatbotWrapper.tsx":{"*":{"id":"(ssr)/./src/components/ChatbotWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Footer.tsx":{"*":{"id":"(ssr)/./src/components/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(ssr)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/SEOSchema.tsx":{"*":{"id":"(ssr)/./src/components/SEOSchema.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/b2b/B2BContact.tsx":{"*":{"id":"(ssr)/./src/components/b2b/B2BContact.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/b2b/B2BHero.tsx":{"*":{"id":"(ssr)/./src/components/b2b/B2BHero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/b2b/B2BPartners.tsx":{"*":{"id":"(ssr)/./src/components/b2b/B2BPartners.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/b2b/B2BProcess.tsx":{"*":{"id":"(ssr)/./src/components/b2b/B2BProcess.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/b2b/B2BServices.tsx":{"*":{"id":"(ssr)/./src/components/b2b/B2BServices.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/contact/ContactForm.tsx":{"*":{"id":"(ssr)/./src/components/contact/ContactForm.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/contact/ContactHero.tsx":{"*":{"id":"(ssr)/./src/components/contact/ContactHero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/contact/ContactInfo.tsx":{"*":{"id":"(ssr)/./src/components/contact/ContactInfo.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/contact/ContactMap.tsx":{"*":{"id":"(ssr)/./src/components/contact/ContactMap.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/pricing/PricingCalculator.tsx":{"*":{"id":"(ssr)/./src/components/pricing/PricingCalculator.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/pricing/PricingFactors.tsx":{"*":{"id":"(ssr)/./src/components/pricing/PricingFactors.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/pricing/PricingFAQ.tsx":{"*":{"id":"(ssr)/./src/components/pricing/PricingFAQ.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/pricing/PricingHero.tsx":{"*":{"id":"(ssr)/./src/components/pricing/PricingHero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/pricing/PricingTables.tsx":{"*":{"id":"(ssr)/./src/components/pricing/PricingTables.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\CertificationsSection.tsx":{"id":"(app-pages-browser)/./src/components/CertificationsSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\EmergencyContact.tsx":{"id":"(app-pages-browser)/./src/components/EmergencyContact.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\HeroSection.tsx":{"id":"(app-pages-browser)/./src/components/HeroSection.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\ServicesOverview.tsx":{"id":"(app-pages-browser)/./src/components/ServicesOverview.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\TestimonialsPreview.tsx":{"id":"(app-pages-browser)/./src/components/TestimonialsPreview.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\WhyChooseUs.tsx":{"id":"(app-pages-browser)/./src/components/WhyChooseUs.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\ChatbotWrapper.tsx":{"id":"(app-pages-browser)/./src/components/ChatbotWrapper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Footer.tsx":{"id":"(app-pages-browser)/./src/components/Footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\Header.tsx":{"id":"(app-pages-browser)/./src/components/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\SEOSchema.tsx":{"id":"(app-pages-browser)/./src/components/SEOSchema.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BContact.tsx":{"id":"(app-pages-browser)/./src/components/b2b/B2BContact.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BHero.tsx":{"id":"(app-pages-browser)/./src/components/b2b/B2BHero.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BPartners.tsx":{"id":"(app-pages-browser)/./src/components/b2b/B2BPartners.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BProcess.tsx":{"id":"(app-pages-browser)/./src/components/b2b/B2BProcess.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\b2b\\B2BServices.tsx":{"id":"(app-pages-browser)/./src/components/b2b/B2BServices.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/uslugi/page","static/chunks/app/uslugi/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/uslugi/page","static/chunks/app/uslugi/page.js"],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactForm.tsx":{"id":"(app-pages-browser)/./src/components/contact/ContactForm.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactHero.tsx":{"id":"(app-pages-browser)/./src/components/contact/ContactHero.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactInfo.tsx":{"id":"(app-pages-browser)/./src/components/contact/ContactInfo.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\contact\\ContactMap.tsx":{"id":"(app-pages-browser)/./src/components/contact/ContactMap.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingCalculator.tsx":{"id":"(app-pages-browser)/./src/components/pricing/PricingCalculator.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFactors.tsx":{"id":"(app-pages-browser)/./src/components/pricing/PricingFactors.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingFAQ.tsx":{"id":"(app-pages-browser)/./src/components/pricing/PricingFAQ.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingHero.tsx":{"id":"(app-pages-browser)/./src/components/pricing/PricingHero.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Downloads\\test2\\src\\components\\pricing\\PricingTables.tsx":{"id":"(app-pages-browser)/./src/components/pricing/PricingTables.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Downloads\\test2\\src\\":[],"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\page":[],"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Downloads\\test2\\src\\app\\uslugi\\page":[]}}