/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/uslugi/usuwanie-skutkow-powodzi/page";
exports.ids = ["app/uslugi/usuwanie-skutkow-powodzi/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&page=%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&appPaths=%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&pagePath=private-next-app-dir%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&page=%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&appPaths=%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&pagePath=private-next-app-dir%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'uslugi',\n        {\n        children: [\n        'usuwanie-skutkow-powodzi',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/uslugi/usuwanie-skutkow-powodzi/page.tsx */ \"(rsc)/./src/app/uslugi/usuwanie-skutkow-powodzi/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/uslugi/usuwanie-skutkow-powodzi/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/uslugi/usuwanie-skutkow-powodzi/page\",\n        pathname: \"/uslugi/usuwanie-skutkow-powodzi\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&page=%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&appPaths=%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&pagePath=private-next-app-dir%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21hcml1JTVDJTVDRG93bmxvYWRzJTVDJTVDdGVzdDIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBa0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zb2x2aWN0dXMtd2Vic2l0ZS8/N2FmNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1hcml1XFxcXERvd25sb2Fkc1xcXFx0ZXN0MlxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CChatbotWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CSEOSchema.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CChatbotWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CSEOSchema.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ChatbotWrapper.tsx */ \"(ssr)/./src/components/ChatbotWrapper.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Footer.tsx */ \"(ssr)/./src/components/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Header.tsx */ \"(ssr)/./src/components/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SEOSchema.tsx */ \"(ssr)/./src/components/SEOSchema.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Poppins%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22weight%5C%22%3A%5B%5C%22300%5C%22%2C%5C%22400%5C%22%2C%5C%22500%5C%22%2C%5C%22600%5C%22%2C%5C%22700%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-poppins%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22poppins%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CChatbotWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmariu%5C%5CDownloads%5C%5Ctest2%5C%5Csrc%5C%5Ccomponents%5C%5CSEOSchema.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatbotWrapper.tsx":
/*!*******************************************!*\
  !*** ./src/components/ChatbotWrapper.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst Chatbot = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\ChatbotWrapper.tsx -> \" + \"./Chatbot\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>null\n});\nconst ChatbotWrapper = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Chatbot, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\ChatbotWrapper.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatbotWrapper);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DaGF0Ym90V3JhcHBlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFa0M7QUFFbEMsTUFBTUMsVUFBVUQsd0RBQU9BOzs7Ozs7OztJQUNyQkUsS0FBSztJQUNMQyxTQUFTLElBQU07O0FBR2pCLE1BQU1DLGlCQUFpQjtJQUNyQixxQkFBTyw4REFBQ0g7Ozs7O0FBQ1Y7QUFFQSxpRUFBZUcsY0FBY0EsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3NvbHZpY3R1cy13ZWJzaXRlLy4vc3JjL2NvbXBvbmVudHMvQ2hhdGJvdFdyYXBwZXIudHN4P2JmMzgiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYydcblxuY29uc3QgQ2hhdGJvdCA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCcuL0NoYXRib3QnKSwge1xuICBzc3I6IGZhbHNlLFxuICBsb2FkaW5nOiAoKSA9PiBudWxsXG59KVxuXG5jb25zdCBDaGF0Ym90V3JhcHBlciA9ICgpID0+IHtcbiAgcmV0dXJuIDxDaGF0Ym90IC8+XG59XG5cbmV4cG9ydCBkZWZhdWx0IENoYXRib3RXcmFwcGVyXG4iXSwibmFtZXMiOlsiZHluYW1pYyIsIkNoYXRib3QiLCJzc3IiLCJsb2FkaW5nIiwiQ2hhdGJvdFdyYXBwZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatbotWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Facebook,Instagram,Linkedin,Mail,MapPin,Phone,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Footer = ()=>{\n    const currentYear = new Date().getFullYear();\n    const services = [\n        \"Sprzątanie po zgonach\",\n        \"Dezynfekcja po śmierci\",\n        \"Sprzątanie po pożarach\",\n        \"Usuwanie skutk\\xf3w powodzi\",\n        \"Ozonowanie pomieszczeń\",\n        \"Usuwanie zapach\\xf3w\"\n    ];\n    const quickLinks = [\n        {\n            name: \"O nas\",\n            href: \"/o-nas\"\n        },\n        {\n            name: \"Usługi\",\n            href: \"/uslugi\"\n        },\n        {\n            name: \"Dla firm\",\n            href: \"/dla-firm\"\n        },\n        {\n            name: \"FAQ\",\n            href: \"/faq\"\n        },\n        {\n            name: \"Kontakt\",\n            href: \"/kontakt\"\n        },\n        {\n            name: \"Galeria realizacji\",\n            href: \"/galeria\"\n        },\n        {\n            name: \"Blog i poradniki\",\n            href: \"/blog\"\n        },\n        {\n            name: \"Opinie klient\\xf3w\",\n            href: \"/opinie\"\n        },\n        {\n            name: \"Cennik usług\",\n            href: \"/cennik\"\n        }\n    ];\n    const legalLinks = [\n        {\n            name: \"Polityka prywatności\",\n            href: \"/polityka-prywatnosci\"\n        },\n        {\n            name: \"Regulamin\",\n            href: \"/regulamin\"\n        },\n        {\n            name: \"RODO\",\n            href: \"/rodo\"\n        },\n        {\n            name: \"Cookies\",\n            href: \"/cookies\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-primary-900 text-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-white rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-primary-900\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 45,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold\",\n                                                    children: \"SOLVICTUS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-300\",\n                                                    children: \"Pomagamy po tragedii\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-300 text-sm leading-relaxed\",\n                                    children: \"Profesjonalne, certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach. Działamy z dyskrecją, empatią i najwyższą skutecznością.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-300 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-300 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-300 hover:text-white transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Nasze usługi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: services.map((service, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/uslugi\",\n                                                className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                                children: service\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Szybkie linki\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2\",\n                                    children: quickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: link.href,\n                                                className: \"text-gray-300 hover:text-white text-sm transition-colors\",\n                                                children: link.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-lg font-semibold mb-4\",\n                                    children: \"Kontakt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"24/7 Dostępność\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: \"Całodobowa linia pomocy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"+48 123 456 789\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 113,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: \"Natychmiastowa pomoc\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: \"Odpowiedź w 24h\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5 text-primary-400 mt-0.5 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Warszawa, Polska\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-300\",\n                                                            children: \"Działamy w całym kraju\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t border-primary-800 mt-8 pt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-300\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        currentYear,\n                                        \" SOLVICTUS. Wszystkie prawa zastrzeżone.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center md:justify-end space-x-4 text-xs\",\n                                    children: legalLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: link.href,\n                                            className: \"text-gray-300 hover:text-white transition-colors\",\n                                            children: link.name\n                                        }, link.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2 text-xs text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Facebook_Instagram_Linkedin_Mail_MapPin_Phone_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Certyfikowane przez Państwowy Zakład Higieny | Licencja nr: PZH/2024/001\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Footer.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Mail,Menu,Phone,Shield,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _LanguageSwitcher__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./LanguageSwitcher */ \"(ssr)/./src/components/LanguageSwitcher.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst Header = ()=>{\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [locale, setLocale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pl\");\n    const [activeDropdown, setActiveDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 50);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const navigation = [\n        {\n            name: \"Strona gł\\xf3wna\",\n            href: \"/\"\n        },\n        {\n            name: \"Usługi\",\n            href: \"/uslugi\",\n            dropdown: [\n                {\n                    name: \"Sprzątanie po zgonach\",\n                    href: \"/uslugi/sprzatanie-po-zgonach\"\n                },\n                {\n                    name: \"Dezynfekcja po śmierci\",\n                    href: \"/uslugi/dezynfekcja-po-smierci\"\n                },\n                {\n                    name: \"Sprzątanie po pożarach\",\n                    href: \"/uslugi/sprzatanie-po-pozarach\"\n                },\n                {\n                    name: \"Usuwanie skutk\\xf3w powodzi\",\n                    href: \"/uslugi/usuwanie-skutkow-powodzi\"\n                },\n                {\n                    name: \"Ozonowanie pomieszczeń\",\n                    href: \"/uslugi/ozonowanie-pomieszczen\"\n                },\n                {\n                    name: \"Usuwanie zapach\\xf3w\",\n                    href: \"/uslugi/usuwanie-zapachow\"\n                },\n                {\n                    name: \"Cennik usług\",\n                    href: \"/cennik\"\n                }\n            ]\n        },\n        {\n            name: \"Oferta\",\n            href: \"#\",\n            dropdown: [\n                {\n                    name: \"O nas\",\n                    href: \"/o-nas\"\n                },\n                {\n                    name: \"Dla firm\",\n                    href: \"/dla-firm\"\n                },\n                {\n                    name: \"Galeria realizacji\",\n                    href: \"/galeria\"\n                },\n                {\n                    name: \"Opinie klient\\xf3w\",\n                    href: \"/opinie\"\n                },\n                {\n                    name: \"FAQ\",\n                    href: \"/faq\"\n                },\n                {\n                    name: \"Blog i poradniki\",\n                    href: \"/blog\"\n                }\n            ]\n        },\n        {\n            name: \"Kontakt\",\n            href: \"/kontakt\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-primary-900 text-white py-2 px-4 text-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto flex flex-col sm:flex-row justify-between items-center space-y-1 sm:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"24/7 Linia pomocy: +48 123 456 789\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Certyfikowana dezynfekcja\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: `sticky top-0 z-50 transition-all duration-300 ${isScrolled ? \"bg-white/95 backdrop-blur-md shadow-lg\" : \"bg-white\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-900 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold gradient-text\",\n                                                    children: \"SOLVICTUS\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 -mt-1\",\n                                                    children: \"Pomagamy po tragedii\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden lg:flex items-center space-x-8\",\n                                    children: navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative group\",\n                                            children: item.dropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                onMouseEnter: ()=>setActiveDropdown(item.name),\n                                                onMouseLeave: ()=>setActiveDropdown(null),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200 relative group flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: item.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 102,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"w-4 h-4 transition-transform duration-200 group-hover:rotate-180\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 103,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 104,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-xl border border-gray-100 py-2 transition-all duration-200 ${activeDropdown === item.name ? \"opacity-100 visible translate-y-0\" : \"opacity-0 invisible -translate-y-2\"}`,\n                                                        children: item.dropdown.map((dropdownItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                href: dropdownItem.href,\n                                                                className: \"block px-4 py-2 text-sm text-gray-700 hover:bg-primary-50 hover:text-primary-600 transition-colors duration-200\",\n                                                                children: dropdownItem.name\n                                                            }, dropdownItem.name, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                                lineNumber: 112,\n                                                                columnNumber: 27\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: item.href,\n                                                className: \"text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200 relative group\",\n                                                children: [\n                                                    item.name,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"absolute -bottom-1 left-0 w-0 h-0.5 bg-primary-500 transition-all duration-300 group-hover:w-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, item.name, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            currentLocale: locale,\n                                            onLocaleChange: setLocale\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/kontakt\",\n                                            className: \"bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 hover:shadow-lg\",\n                                            children: \"Zgłoś sytuację\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"lg:hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LanguageSwitcher__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                currentLocale: locale,\n                                                onLocaleChange: setLocale\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                            className: \"p-2 rounded-md text-gray-700 hover:text-primary-600 hover:bg-gray-100\",\n                                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 31\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 59\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:hidden bg-white border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-4 py-2 space-y-1\",\n                            children: [\n                                navigation.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: item.dropdown ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setActiveDropdown(activeDropdown === item.name ? null : item.name),\n                                                    className: \"flex items-center justify-between w-full px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 179,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Mail_Menu_Phone_Shield_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: `w-4 h-4 transition-transform duration-200 ${activeDropdown === item.name ? \"rotate-180\" : \"\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                activeDropdown === item.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ml-4 mt-1 space-y-1\",\n                                                    children: item.dropdown.map((dropdownItem)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            href: dropdownItem.href,\n                                                            className: \"block px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md\",\n                                                            onClick: ()=>setIsMenuOpen(false),\n                                                            children: dropdownItem.name\n                                                        }, dropdownItem.name, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 29\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 21\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: item.href,\n                                            className: \"block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md font-medium\",\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            children: item.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, item.name, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, undefined)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-2 border-t border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/kontakt\",\n                                        className: \"block w-full text-center bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium\",\n                                        onClick: ()=>setIsMenuOpen(false),\n                                        children: \"Zgłoś sytuację\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Header);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9IZWFkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ2Y7QUFDNEM7QUFDdkI7QUFFakQsTUFBTVUsU0FBUztJQUNiLE1BQU0sQ0FBQ0MsWUFBWUMsY0FBYyxHQUFHWiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNhLFlBQVlDLGNBQWMsR0FBR2QsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZSxRQUFRQyxVQUFVLEdBQUdoQiwrQ0FBUUEsQ0FBYztJQUNsRCxNQUFNLENBQUNpQixnQkFBZ0JDLGtCQUFrQixHQUFHbEIsK0NBQVFBLENBQWdCO0lBRXBFQyxnREFBU0EsQ0FBQztRQUNSLE1BQU1rQixlQUFlO1lBQ25CTCxjQUFjTSxPQUFPQyxPQUFPLEdBQUc7UUFDakM7UUFDQUQsT0FBT0UsZ0JBQWdCLENBQUMsVUFBVUg7UUFDbEMsT0FBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjtJQUNwRCxHQUFHLEVBQUU7SUFFTCxNQUFNSyxhQUFhO1FBQ2pCO1lBQUVDLE1BQU07WUFBaUJDLE1BQU07UUFBSTtRQUNuQztZQUNFRCxNQUFNO1lBQ05DLE1BQU07WUFDTkMsVUFBVTtnQkFDUjtvQkFBRUYsTUFBTTtvQkFBeUJDLE1BQU07Z0JBQWdDO2dCQUN2RTtvQkFBRUQsTUFBTTtvQkFBMEJDLE1BQU07Z0JBQWlDO2dCQUN6RTtvQkFBRUQsTUFBTTtvQkFBMEJDLE1BQU07Z0JBQWlDO2dCQUN6RTtvQkFBRUQsTUFBTTtvQkFBNEJDLE1BQU07Z0JBQW1DO2dCQUM3RTtvQkFBRUQsTUFBTTtvQkFBMEJDLE1BQU07Z0JBQWlDO2dCQUN6RTtvQkFBRUQsTUFBTTtvQkFBcUJDLE1BQU07Z0JBQTRCO2dCQUMvRDtvQkFBRUQsTUFBTTtvQkFBZ0JDLE1BQU07Z0JBQVU7YUFDekM7UUFDSDtRQUNBO1lBQ0VELE1BQU07WUFDTkMsTUFBTTtZQUNOQyxVQUFVO2dCQUNSO29CQUFFRixNQUFNO29CQUFTQyxNQUFNO2dCQUFTO2dCQUNoQztvQkFBRUQsTUFBTTtvQkFBWUMsTUFBTTtnQkFBWTtnQkFDdEM7b0JBQUVELE1BQU07b0JBQXNCQyxNQUFNO2dCQUFXO2dCQUMvQztvQkFBRUQsTUFBTTtvQkFBbUJDLE1BQU07Z0JBQVU7Z0JBQzNDO29CQUFFRCxNQUFNO29CQUFPQyxNQUFNO2dCQUFPO2dCQUM1QjtvQkFBRUQsTUFBTTtvQkFBb0JDLE1BQU07Z0JBQVE7YUFDM0M7UUFDSDtRQUNBO1lBQUVELE1BQU07WUFBV0MsTUFBTTtRQUFXO0tBQ3JDO0lBRUQscUJBQ0U7OzBCQUVFLDhEQUFDRTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3hCLGdIQUFLQTs0Q0FBQ3dCLFdBQVU7Ozs7OztzREFDakIsOERBQUNDO3NEQUFLOzs7Ozs7Ozs7Ozs7OENBRVIsOERBQUNGO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3ZCLGdIQUFJQTs0Q0FBQ3VCLFdBQVU7Ozs7OztzREFDaEIsOERBQUNDO3NEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBR1YsOERBQUNGOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3RCLGdIQUFNQTtvQ0FBQ3NCLFdBQVU7Ozs7Ozs4Q0FDbEIsOERBQUNDO29DQUFLRCxXQUFVOzhDQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNaEMsOERBQUNFO2dCQUFPRixXQUFXLENBQUMsOENBQThDLEVBQ2hFaEIsYUFBYSwyQ0FBMkMsV0FDekQsQ0FBQzs7a0NBQ0EsOERBQUNlO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUMzQixpREFBSUE7b0NBQUN3QixNQUFLO29DQUFJRyxXQUFVOztzREFDdkIsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDdEIsZ0hBQU1BO2dEQUFDc0IsV0FBVTs7Ozs7Ozs7Ozs7c0RBRXBCLDhEQUFDRDs7OERBQ0MsOERBQUNJO29EQUFHSCxXQUFVOzhEQUFtQzs7Ozs7OzhEQUNqRCw4REFBQ0k7b0RBQUVKLFdBQVU7OERBQThCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSy9DLDhEQUFDSztvQ0FBSUwsV0FBVTs4Q0FDWkwsV0FBV1csR0FBRyxDQUFDLENBQUNDLHFCQUNmLDhEQUFDUjs0Q0FBb0JDLFdBQVU7c0RBQzVCTyxLQUFLVCxRQUFRLGlCQUNaLDhEQUFDQztnREFDQ0MsV0FBVTtnREFDVlEsY0FBYyxJQUFNbkIsa0JBQWtCa0IsS0FBS1gsSUFBSTtnREFDL0NhLGNBQWMsSUFBTXBCLGtCQUFrQjs7a0VBRXRDLDhEQUFDcUI7d0RBQU9WLFdBQVU7OzBFQUNoQiw4REFBQ0M7MEVBQU1NLEtBQUtYLElBQUk7Ozs7OzswRUFDaEIsOERBQUNqQixnSEFBV0E7Z0VBQUNxQixXQUFVOzs7Ozs7MEVBQ3ZCLDhEQUFDQztnRUFBS0QsV0FBVTs7Ozs7Ozs7Ozs7O2tFQUlsQiw4REFBQ0Q7d0RBQUlDLFdBQVcsQ0FBQyx5SEFBeUgsRUFDeElaLG1CQUFtQm1CLEtBQUtYLElBQUksR0FBRyxzQ0FBc0MscUNBQ3RFLENBQUM7a0VBQ0NXLEtBQUtULFFBQVEsQ0FBQ1EsR0FBRyxDQUFDLENBQUNLLDZCQUNsQiw4REFBQ3RDLGlEQUFJQTtnRUFFSHdCLE1BQU1jLGFBQWFkLElBQUk7Z0VBQ3ZCRyxXQUFVOzBFQUVUVyxhQUFhZixJQUFJOytEQUpiZSxhQUFhZixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7MEVBVTlCLDhEQUFDdkIsaURBQUlBO2dEQUNId0IsTUFBTVUsS0FBS1YsSUFBSTtnREFDZkcsV0FBVTs7b0RBRVRPLEtBQUtYLElBQUk7a0VBQ1YsOERBQUNLO3dEQUFLRCxXQUFVOzs7Ozs7Ozs7Ozs7MkNBbENaTyxLQUFLWCxJQUFJOzs7Ozs7Ozs7OzhDQTBDdkIsOERBQUNHO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3BCLHlEQUFnQkE7NENBQ2ZnQyxlQUFlMUI7NENBQ2YyQixnQkFBZ0IxQjs7Ozs7O3NEQUVsQiw4REFBQ2QsaURBQUlBOzRDQUNId0IsTUFBSzs0Q0FDTEcsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7OzhDQU1ILDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDcEIseURBQWdCQTtnREFDZmdDLGVBQWUxQjtnREFDZjJCLGdCQUFnQjFCOzs7Ozs7Ozs7OztzREFHcEIsOERBQUN1Qjs0Q0FDQ0ksU0FBUyxJQUFNL0IsY0FBYyxDQUFDRDs0Q0FDOUJrQixXQUFVO3NEQUVUbEIsMkJBQWEsOERBQUNQLGdIQUFDQTtnREFBQ3lCLFdBQVU7Ozs7OzBFQUFlLDhEQUFDMUIsZ0hBQUlBO2dEQUFDMEIsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFPakVsQiw0QkFDQyw4REFBQ2lCO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTs7Z0NBQ1pMLFdBQVdXLEdBQUcsQ0FBQyxDQUFDQyxxQkFDZiw4REFBQ1I7a0RBQ0VRLEtBQUtULFFBQVEsaUJBQ1osOERBQUNDOzs4REFDQyw4REFBQ1c7b0RBQ0NJLFNBQVMsSUFBTXpCLGtCQUFrQkQsbUJBQW1CbUIsS0FBS1gsSUFBSSxHQUFHLE9BQU9XLEtBQUtYLElBQUk7b0RBQ2hGSSxXQUFVOztzRUFFViw4REFBQ0M7c0VBQU1NLEtBQUtYLElBQUk7Ozs7OztzRUFDaEIsOERBQUNqQixnSEFBV0E7NERBQUNxQixXQUFXLENBQUMsMENBQTBDLEVBQ2pFWixtQkFBbUJtQixLQUFLWCxJQUFJLEdBQUcsZUFBZSxHQUMvQyxDQUFDOzs7Ozs7Ozs7Ozs7Z0RBRUhSLG1CQUFtQm1CLEtBQUtYLElBQUksa0JBQzNCLDhEQUFDRztvREFBSUMsV0FBVTs4REFDWk8sS0FBS1QsUUFBUSxDQUFDUSxHQUFHLENBQUMsQ0FBQ0ssNkJBQ2xCLDhEQUFDdEMsaURBQUlBOzREQUVId0IsTUFBTWMsYUFBYWQsSUFBSTs0REFDdkJHLFdBQVU7NERBQ1ZjLFNBQVMsSUFBTS9CLGNBQWM7c0VBRTVCNEIsYUFBYWYsSUFBSTsyREFMYmUsYUFBYWYsSUFBSTs7Ozs7Ozs7Ozs7Ozs7O3NFQVloQyw4REFBQ3ZCLGlEQUFJQTs0Q0FDSHdCLE1BQU1VLEtBQUtWLElBQUk7NENBQ2ZHLFdBQVU7NENBQ1ZjLFNBQVMsSUFBTS9CLGNBQWM7c0RBRTVCd0IsS0FBS1gsSUFBSTs7Ozs7O3VDQWpDTlcsS0FBS1gsSUFBSTs7Ozs7OENBc0NyQiw4REFBQ0c7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUMzQixpREFBSUE7d0NBQ0h3QixNQUFLO3dDQUNMRyxXQUFVO3dDQUNWYyxTQUFTLElBQU0vQixjQUFjO2tEQUM5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVWpCO0FBRUEsaUVBQWVGLE1BQU1BLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zb2x2aWN0dXMtd2Vic2l0ZS8uL3NyYy9jb21wb25lbnRzL0hlYWRlci50c3g/YTY5NiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgTWVudSwgWCwgUGhvbmUsIE1haWwsIFNoaWVsZCwgQ2hldnJvbkRvd24gfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgTGFuZ3VhZ2VTd2l0Y2hlciBmcm9tICcuL0xhbmd1YWdlU3dpdGNoZXInXG5cbmNvbnN0IEhlYWRlciA9ICgpID0+IHtcbiAgY29uc3QgW2lzTWVudU9wZW4sIHNldElzTWVudU9wZW5dID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpc1Njcm9sbGVkLCBzZXRJc1Njcm9sbGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbbG9jYWxlLCBzZXRMb2NhbGVdID0gdXNlU3RhdGU8J3BsJyB8ICdlbic+KCdwbCcpXG4gIGNvbnN0IFthY3RpdmVEcm9wZG93biwgc2V0QWN0aXZlRHJvcGRvd25dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZVNjcm9sbCA9ICgpID0+IHtcbiAgICAgIHNldElzU2Nyb2xsZWQod2luZG93LnNjcm9sbFkgPiA1MClcbiAgICB9XG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIGhhbmRsZVNjcm9sbClcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIGhhbmRsZVNjcm9sbClcbiAgfSwgW10pXG5cbiAgY29uc3QgbmF2aWdhdGlvbiA9IFtcbiAgICB7IG5hbWU6ICdTdHJvbmEgZ8WCw7N3bmEnLCBocmVmOiAnLycgfSxcbiAgICB7XG4gICAgICBuYW1lOiAnVXPFgnVnaScsXG4gICAgICBocmVmOiAnL3VzbHVnaScsXG4gICAgICBkcm9wZG93bjogW1xuICAgICAgICB7IG5hbWU6ICdTcHJ6xIV0YW5pZSBwbyB6Z29uYWNoJywgaHJlZjogJy91c2x1Z2kvc3ByemF0YW5pZS1wby16Z29uYWNoJyB9LFxuICAgICAgICB7IG5hbWU6ICdEZXp5bmZla2NqYSBwbyDFm21pZXJjaScsIGhyZWY6ICcvdXNsdWdpL2RlenluZmVrY2phLXBvLXNtaWVyY2knIH0sXG4gICAgICAgIHsgbmFtZTogJ1NwcnrEhXRhbmllIHBvIHBvxbxhcmFjaCcsIGhyZWY6ICcvdXNsdWdpL3NwcnphdGFuaWUtcG8tcG96YXJhY2gnIH0sXG4gICAgICAgIHsgbmFtZTogJ1VzdXdhbmllIHNrdXRrw7N3IHBvd29kemknLCBocmVmOiAnL3VzbHVnaS91c3V3YW5pZS1za3V0a293LXBvd29kemknIH0sXG4gICAgICAgIHsgbmFtZTogJ096b25vd2FuaWUgcG9taWVzemN6ZcWEJywgaHJlZjogJy91c2x1Z2kvb3pvbm93YW5pZS1wb21pZXN6Y3plbicgfSxcbiAgICAgICAgeyBuYW1lOiAnVXN1d2FuaWUgemFwYWNow7N3JywgaHJlZjogJy91c2x1Z2kvdXN1d2FuaWUtemFwYWNob3cnIH0sXG4gICAgICAgIHsgbmFtZTogJ0Nlbm5payB1c8WCdWcnLCBocmVmOiAnL2Nlbm5paycgfSxcbiAgICAgIF1cbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICdPZmVydGEnLFxuICAgICAgaHJlZjogJyMnLFxuICAgICAgZHJvcGRvd246IFtcbiAgICAgICAgeyBuYW1lOiAnTyBuYXMnLCBocmVmOiAnL28tbmFzJyB9LFxuICAgICAgICB7IG5hbWU6ICdEbGEgZmlybScsIGhyZWY6ICcvZGxhLWZpcm0nIH0sXG4gICAgICAgIHsgbmFtZTogJ0dhbGVyaWEgcmVhbGl6YWNqaScsIGhyZWY6ICcvZ2FsZXJpYScgfSxcbiAgICAgICAgeyBuYW1lOiAnT3BpbmllIGtsaWVudMOzdycsIGhyZWY6ICcvb3BpbmllJyB9LFxuICAgICAgICB7IG5hbWU6ICdGQVEnLCBocmVmOiAnL2ZhcScgfSxcbiAgICAgICAgeyBuYW1lOiAnQmxvZyBpIHBvcmFkbmlraScsIGhyZWY6ICcvYmxvZycgfSxcbiAgICAgIF1cbiAgICB9LFxuICAgIHsgbmFtZTogJ0tvbnRha3QnLCBocmVmOiAnL2tvbnRha3QnIH0sXG4gIF1cblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICB7LyogVG9wIGJhciAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctcHJpbWFyeS05MDAgdGV4dC13aGl0ZSBweS0yIHB4LTQgdGV4dC1zbVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIGZsZXggZmxleC1jb2wgc206ZmxleC1yb3cganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBzcGFjZS15LTEgc206c3BhY2UteS0wXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxQaG9uZSBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4+MjQvNyBMaW5pYSBwb21vY3k6ICs0OCAxMjMgNDU2IDc4OTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6ZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxNYWlsIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8c3Bhbj5rb250YWt0QHNvbHZpY3R1cy5wbDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14c1wiPkNlcnR5Zmlrb3dhbmEgZGV6eW5mZWtjamE8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBNYWluIGhlYWRlciAqL31cbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPXtgc3RpY2t5IHRvcC0wIHotNTAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XG4gICAgICAgIGlzU2Nyb2xsZWQgPyAnYmctd2hpdGUvOTUgYmFja2Ryb3AtYmx1ci1tZCBzaGFkb3ctbGcnIDogJ2JnLXdoaXRlJ1xuICAgICAgfWB9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgcHktNFwiPlxuICAgICAgICAgICAgey8qIExvZ28gKi99XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMTAgaC0xMCBiZy1ncmFkaWVudC10by1iciBmcm9tLXByaW1hcnktNTAwIHRvLXByaW1hcnktOTAwIHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8U2hpZWxkIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgZ3JhZGllbnQtdGV4dFwiPlNPTFZJQ1RVUzwvaDE+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNjAwIC1tdC0xXCI+UG9tYWdhbXkgcG8gdHJhZ2VkaWk8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9MaW5rPlxuXG4gICAgICAgICAgICB7LyogRGVza3RvcCBOYXZpZ2F0aW9uICovfVxuICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6ZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC04XCI+XG4gICAgICAgICAgICAgIHtuYXZpZ2F0aW9uLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpdGVtLm5hbWV9IGNsYXNzTmFtZT1cInJlbGF0aXZlIGdyb3VwXCI+XG4gICAgICAgICAgICAgICAgICB7aXRlbS5kcm9wZG93biA/IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eygpID0+IHNldEFjdGl2ZURyb3Bkb3duKGl0ZW0ubmFtZSl9XG4gICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoKSA9PiBzZXRBY3RpdmVEcm9wZG93bihudWxsKX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b24gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBob3Zlcjp0ZXh0LXByaW1hcnktNjAwIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCByZWxhdGl2ZSBncm91cCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntpdGVtLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT1cInctNCBoLTQgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwIGdyb3VwLWhvdmVyOnJvdGF0ZS0xODBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0xIGxlZnQtMCB3LTAgaC0wLjUgYmctcHJpbWFyeS01MDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGdyb3VwLWhvdmVyOnctZnVsbFwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBEcm9wZG93biBNZW51ICovfVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYWJzb2x1dGUgdG9wLWZ1bGwgbGVmdC0wIG10LTIgdy01NiBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy14bCBib3JkZXIgYm9yZGVyLWdyYXktMTAwIHB5LTIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgICBhY3RpdmVEcm9wZG93biA9PT0gaXRlbS5uYW1lID8gJ29wYWNpdHktMTAwIHZpc2libGUgdHJhbnNsYXRlLXktMCcgOiAnb3BhY2l0eS0wIGludmlzaWJsZSAtdHJhbnNsYXRlLXktMidcbiAgICAgICAgICAgICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5kcm9wZG93bi5tYXAoKGRyb3Bkb3duSXRlbSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17ZHJvcGRvd25JdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj17ZHJvcGRvd25JdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmxvY2sgcHgtNCBweS0yIHRleHQtc20gdGV4dC1ncmF5LTcwMCBob3ZlcjpiZy1wcmltYXJ5LTUwIGhvdmVyOnRleHQtcHJpbWFyeS02MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkcm9wZG93bkl0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICBocmVmPXtpdGVtLmhyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBob3Zlcjp0ZXh0LXByaW1hcnktNjAwIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMCByZWxhdGl2ZSBncm91cFwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIC1ib3R0b20tMSBsZWZ0LTAgdy0wIGgtMC41IGJnLXByaW1hcnktNTAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBncm91cC1ob3Zlcjp3LWZ1bGxcIj48L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9uYXY+XG5cbiAgICAgICAgICAgIHsvKiBMYW5ndWFnZSBzd2l0Y2hlciBhbmQgQ1RBIEJ1dHRvbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICA8TGFuZ3VhZ2VTd2l0Y2hlclxuICAgICAgICAgICAgICAgIGN1cnJlbnRMb2NhbGU9e2xvY2FsZX1cbiAgICAgICAgICAgICAgICBvbkxvY2FsZUNoYW5nZT17c2V0TG9jYWxlfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGhyZWY9XCIva29udGFrdFwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctcHJpbWFyeS02MDAgaG92ZXI6YmctcHJpbWFyeS03MDAgdGV4dC13aGl0ZSBweC02IHB5LTIgcm91bmRlZC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2hhZG93LWxnXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIFpnxYJvxZsgc3l0dWFjasSZXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTW9iaWxlIGxhbmd1YWdlIHN3aXRjaGVyIGFuZCBtZW51IGJ1dHRvbiAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6aGlkZGVuIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmhpZGRlblwiPlxuICAgICAgICAgICAgICAgIDxMYW5ndWFnZVN3aXRjaGVyXG4gICAgICAgICAgICAgICAgICBjdXJyZW50TG9jYWxlPXtsb2NhbGV9XG4gICAgICAgICAgICAgICAgICBvbkxvY2FsZUNoYW5nZT17c2V0TG9jYWxlfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbighaXNNZW51T3Blbil9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtbWQgdGV4dC1ncmF5LTcwMCBob3Zlcjp0ZXh0LXByaW1hcnktNjAwIGhvdmVyOmJnLWdyYXktMTAwXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpc01lbnVPcGVuID8gPFggY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+IDogPE1lbnUgY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+fVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTW9iaWxlIE5hdmlnYXRpb24gKi99XG4gICAgICAgIHtpc01lbnVPcGVuICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxnOmhpZGRlbiBiZy13aGl0ZSBib3JkZXItdCBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNCBweS0yIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICB7bmF2aWdhdGlvbi5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17aXRlbS5uYW1lfT5cbiAgICAgICAgICAgICAgICAgIHtpdGVtLmRyb3Bkb3duID8gKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZURyb3Bkb3duKGFjdGl2ZURyb3Bkb3duID09PSBpdGVtLm5hbWUgPyBudWxsIDogaXRlbS5uYW1lKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB3LWZ1bGwgcHgtMyBweS0yIHRleHQtZ3JheS03MDAgaG92ZXI6dGV4dC1wcmltYXJ5LTYwMCBob3ZlcjpiZy1ncmF5LTUwIHJvdW5kZWQtbWQgZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPntpdGVtLm5hbWV9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPENoZXZyb25Eb3duIGNsYXNzTmFtZT17YHctNCBoLTQgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGFjdGl2ZURyb3Bkb3duID09PSBpdGVtLm5hbWUgPyAncm90YXRlLTE4MCcgOiAnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgfWB9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAge2FjdGl2ZURyb3Bkb3duID09PSBpdGVtLm5hbWUgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC00IG10LTEgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmRyb3Bkb3duLm1hcCgoZHJvcGRvd25JdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17ZHJvcGRvd25JdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPXtkcm9wZG93bkl0ZW0uaHJlZn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTMgcHktMiB0ZXh0LXNtIHRleHQtZ3JheS02MDAgaG92ZXI6dGV4dC1wcmltYXJ5LTYwMCBob3ZlcjpiZy1ncmF5LTUwIHJvdW5kZWQtbWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNNZW51T3BlbihmYWxzZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Ryb3Bkb3duSXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHB4LTMgcHktMiB0ZXh0LWdyYXktNzAwIGhvdmVyOnRleHQtcHJpbWFyeS02MDAgaG92ZXI6YmctZ3JheS01MCByb3VuZGVkLW1kIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB0LTIgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGhyZWY9XCIva29udGFrdFwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJibG9jayB3LWZ1bGwgdGV4dC1jZW50ZXIgYmctcHJpbWFyeS02MDAgaG92ZXI6YmctcHJpbWFyeS03MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1sZyBmb250LW1lZGl1bVwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBaZ8WCb8WbIHN5dHVhY2rEmVxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cbiAgICAgIDwvaGVhZGVyPlxuICAgIDwvPlxuICApXG59XG5cbmV4cG9ydCBkZWZhdWx0IEhlYWRlclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiTGluayIsIk1lbnUiLCJYIiwiUGhvbmUiLCJNYWlsIiwiU2hpZWxkIiwiQ2hldnJvbkRvd24iLCJMYW5ndWFnZVN3aXRjaGVyIiwiSGVhZGVyIiwiaXNNZW51T3BlbiIsInNldElzTWVudU9wZW4iLCJpc1Njcm9sbGVkIiwic2V0SXNTY3JvbGxlZCIsImxvY2FsZSIsInNldExvY2FsZSIsImFjdGl2ZURyb3Bkb3duIiwic2V0QWN0aXZlRHJvcGRvd24iLCJoYW5kbGVTY3JvbGwiLCJ3aW5kb3ciLCJzY3JvbGxZIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJuYXZpZ2F0aW9uIiwibmFtZSIsImhyZWYiLCJkcm9wZG93biIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJoZWFkZXIiLCJoMSIsInAiLCJuYXYiLCJtYXAiLCJpdGVtIiwib25Nb3VzZUVudGVyIiwib25Nb3VzZUxlYXZlIiwiYnV0dG9uIiwiZHJvcGRvd25JdGVtIiwiY3VycmVudExvY2FsZSIsIm9uTG9jYWxlQ2hhbmdlIiwib25DbGljayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/LanguageSwitcher.tsx":
/*!*********************************************!*\
  !*** ./src/components/LanguageSwitcher.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Globe!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n */ \"(ssr)/./src/lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst LanguageSwitcher = ({ currentLocale, onLocaleChange })=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const languages = {\n        pl: {\n            name: \"Polski\",\n            flag: \"\\uD83C\\uDDF5\\uD83C\\uDDF1\"\n        },\n        en: {\n            name: \"English\",\n            flag: \"\\uD83C\\uDDEC\\uD83C\\uDDE7\"\n        }\n    };\n    const handleLocaleChange = (locale)=>{\n        onLocaleChange(locale);\n        setIsOpen(false);\n        // Store preference in localStorage (only in browser)\n        if (false) {}\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-primary-600 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium\",\n                        children: [\n                            languages[currentLocale].flag,\n                            \" \",\n                            languages[currentLocale].name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Globe_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: `w-4 h-4 transition-transform ${isOpen ? \"rotate-180\" : \"\"}`\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-10\",\n                        onClick: ()=>setIsOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-2\",\n                            children: _lib_i18n__WEBPACK_IMPORTED_MODULE_2__.locales.map((locale)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleLocaleChange(locale),\n                                    className: `w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors ${currentLocale === locale ? \"bg-primary-50 text-primary-600\" : \"text-gray-700\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-lg\",\n                                            children: languages[locale].flag\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: languages[locale].name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        currentLocale === locale && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-auto text-primary-600\",\n                                            children: \"✓\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, locale, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\LanguageSwitcher.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LanguageSwitcher);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/LanguageSwitcher.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SEOSchema.tsx":
/*!**************************************!*\
  !*** ./src/components/SEOSchema.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst SEOSchema = ({ type = \"organization\", title, description, url })=>{\n    const organizationSchema = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Organization\",\n        \"name\": \"SOLVICTUS\",\n        \"description\": \"Profesjonalne, certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach\",\n        \"url\": \"https://solvictus.pl\",\n        \"logo\": \"https://solvictus.pl/logo.png\",\n        \"contactPoint\": {\n            \"@type\": \"ContactPoint\",\n            \"telephone\": \"+**************\",\n            \"contactType\": \"emergency\",\n            \"availableLanguage\": \"Polish\",\n            \"hoursAvailable\": \"24/7\"\n        },\n        \"address\": {\n            \"@type\": \"PostalAddress\",\n            \"streetAddress\": \"ul. Przykładowa 123\",\n            \"addressLocality\": \"Warszawa\",\n            \"postalCode\": \"00-001\",\n            \"addressCountry\": \"PL\"\n        },\n        \"areaServed\": {\n            \"@type\": \"Country\",\n            \"name\": \"Poland\"\n        },\n        \"serviceType\": [\n            \"Sprzątanie po zgonach\",\n            \"Dezynfekcja po śmierci\",\n            \"Sprzątanie po pożarach\",\n            \"Usuwanie skutk\\xf3w powodzi\",\n            \"Ozonowanie pomieszczeń\",\n            \"Dezynfekcja biologiczna\"\n        ],\n        \"hasCredential\": [\n            {\n                \"@type\": \"EducationalOccupationalCredential\",\n                \"name\": \"Certyfikat PZH\",\n                \"credentialCategory\": \"Dezynfekcja\"\n            },\n            {\n                \"@type\": \"EducationalOccupationalCredential\",\n                \"name\": \"ISO 9001:2015\",\n                \"credentialCategory\": \"Zarządzanie jakością\"\n            }\n        ]\n    };\n    const serviceSchema = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"Service\",\n        \"name\": title || \"Profesjonalne sprzątanie po tragedii\",\n        \"description\": description || \"Certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach\",\n        \"provider\": {\n            \"@type\": \"Organization\",\n            \"name\": \"SOLVICTUS\",\n            \"url\": \"https://solvictus.pl\"\n        },\n        \"areaServed\": {\n            \"@type\": \"Country\",\n            \"name\": \"Poland\"\n        },\n        \"availableChannel\": {\n            \"@type\": \"ServiceChannel\",\n            \"servicePhone\": \"+**************\",\n            \"serviceUrl\": \"https://solvictus.pl/kontakt\"\n        },\n        \"hoursAvailable\": \"24/7\",\n        \"category\": \"Cleaning Services\"\n    };\n    const localBusinessSchema = {\n        \"@context\": \"https://schema.org\",\n        \"@type\": \"LocalBusiness\",\n        \"name\": \"SOLVICTUS\",\n        \"image\": \"https://solvictus.pl/logo.png\",\n        \"telephone\": \"+**************\",\n        \"email\": \"<EMAIL>\",\n        \"address\": {\n            \"@type\": \"PostalAddress\",\n            \"streetAddress\": \"ul. Przykładowa 123\",\n            \"addressLocality\": \"Warszawa\",\n            \"postalCode\": \"00-001\",\n            \"addressCountry\": \"PL\"\n        },\n        \"geo\": {\n            \"@type\": \"GeoCoordinates\",\n            \"latitude\": 52.2297,\n            \"longitude\": 21.0122\n        },\n        \"url\": \"https://solvictus.pl\",\n        \"openingHours\": \"Mo-Su 00:00-23:59\",\n        \"priceRange\": \"$$\",\n        \"aggregateRating\": {\n            \"@type\": \"AggregateRating\",\n            \"ratingValue\": \"4.9\",\n            \"reviewCount\": \"127\"\n        }\n    };\n    const getSchema = ()=>{\n        switch(type){\n            case \"service\":\n                return serviceSchema;\n            case \"organization\":\n                return [\n                    organizationSchema,\n                    localBusinessSchema\n                ];\n            default:\n                return organizationSchema;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"structured-data\",\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(getSchema())\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\components\\\\SEOSchema.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SEOSchema);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SEOSchema.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/i18n.ts":
/*!*************************!*\
  !*** ./src/lib/i18n.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation),\n/* harmony export */   locales: () => (/* binding */ locales),\n/* harmony export */   t: () => (/* binding */ t),\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\nconst defaultLocale = \"pl\";\nconst locales = [\n    \"pl\",\n    \"en\"\n];\nconst translations = {\n    pl: {\n        // Navigation\n        nav: {\n            home: \"Strona gł\\xf3wna\",\n            about: \"O nas\",\n            services: \"Usługi\",\n            business: \"Dla firm\",\n            gallery: \"Galeria\",\n            blog: \"Blog\",\n            reviews: \"Opinie\",\n            pricing: \"Cennik\",\n            faq: \"FAQ\",\n            contact: \"Kontakt\"\n        },\n        // Common\n        common: {\n            readMore: \"Czytaj więcej\",\n            learnMore: \"Dowiedz się więcej\",\n            contactUs: \"Skontaktuj się z nami\",\n            callNow: \"Zadzwoń teraz\",\n            freeQuote: \"Bezpłatna wycena\",\n            emergency: \"Sytuacja kryzysowa\",\n            available247: \"Dostępni 24/7\",\n            phone: \"Telefon\",\n            email: \"Email\",\n            address: \"Adres\",\n            loading: \"Ładowanie...\",\n            submit: \"Wyślij\",\n            cancel: \"Anuluj\",\n            close: \"Zamknij\",\n            next: \"Następny\",\n            previous: \"Poprzedni\",\n            showMore: \"Pokaż więcej\",\n            showLess: \"Pokaż mniej\"\n        },\n        // Hero section\n        hero: {\n            title: \"Profesjonalne sprzątanie po tragedii\",\n            subtitle: \"Certyfikowane usługi sprzątania i dezynfekcji po traumatycznych wydarzeniach. Pomagamy przywr\\xf3cić bezpieczeństwo i higienę z pełną dyskrecją.\",\n            emergencyLine: \"Linia kryzysowa 24/7\",\n            reportSituation: \"Zgłoś sytuację\",\n            ourServices: \"Nasze usługi\"\n        },\n        // Services\n        services: {\n            deathCleanup: \"Sprzątanie po zgonach\",\n            fireCleanup: \"Sprzątanie po pożarach\",\n            floodCleanup: \"Usuwanie skutk\\xf3w powodzi\",\n            biologicalDisinfection: \"Dezynfekcja biologiczna\",\n            ozonetreatment: \"Ozonowanie pomieszczeń\",\n            businessServices: \"Usługi dla firm\"\n        },\n        // Footer\n        footer: {\n            tagline: \"Pomagamy po tragedii\",\n            quickLinks: \"Szybkie linki\",\n            services: \"Usługi\",\n            company: \"Firma\",\n            contact: \"Kontakt\",\n            legal: \"Informacje prawne\",\n            privacy: \"Polityka prywatności\",\n            terms: \"Regulamin\",\n            cookies: \"Polityka cookies\",\n            rodo: \"RODO\",\n            copyright: \"Wszystkie prawa zastrzeżone.\",\n            emergencyContact: \"Kontakt kryzysowy\",\n            businessHours: \"Godziny pracy biura\",\n            businessHoursTime: \"Pn-Pt: 8:00-18:00\",\n            emergencyAvailable: \"Interwencje: 24/7\"\n        }\n    },\n    en: {\n        // Navigation\n        nav: {\n            home: \"Home\",\n            about: \"About Us\",\n            services: \"Services\",\n            business: \"For Business\",\n            gallery: \"Gallery\",\n            blog: \"Blog\",\n            reviews: \"Reviews\",\n            pricing: \"Pricing\",\n            faq: \"FAQ\",\n            contact: \"Contact\"\n        },\n        // Common\n        common: {\n            readMore: \"Read more\",\n            learnMore: \"Learn more\",\n            contactUs: \"Contact us\",\n            callNow: \"Call now\",\n            freeQuote: \"Free quote\",\n            emergency: \"Emergency situation\",\n            available247: \"Available 24/7\",\n            phone: \"Phone\",\n            email: \"Email\",\n            address: \"Address\",\n            loading: \"Loading...\",\n            submit: \"Submit\",\n            cancel: \"Cancel\",\n            close: \"Close\",\n            next: \"Next\",\n            previous: \"Previous\",\n            showMore: \"Show more\",\n            showLess: \"Show less\"\n        },\n        // Hero section\n        hero: {\n            title: \"Professional trauma cleaning services\",\n            subtitle: \"Certified cleaning and disinfection services after traumatic events. We help restore safety and hygiene with complete discretion.\",\n            emergencyLine: \"24/7 Emergency line\",\n            reportSituation: \"Report situation\",\n            ourServices: \"Our services\"\n        },\n        // Services\n        services: {\n            deathCleanup: \"Death cleanup\",\n            fireCleanup: \"Fire damage cleanup\",\n            floodCleanup: \"Flood damage restoration\",\n            biologicalDisinfection: \"Biological disinfection\",\n            ozonetreatment: \"Ozone treatment\",\n            businessServices: \"Business services\"\n        },\n        // Footer\n        footer: {\n            tagline: \"Helping after tragedy\",\n            quickLinks: \"Quick links\",\n            services: \"Services\",\n            company: \"Company\",\n            contact: \"Contact\",\n            legal: \"Legal information\",\n            privacy: \"Privacy policy\",\n            terms: \"Terms of service\",\n            cookies: \"Cookie policy\",\n            rodo: \"GDPR\",\n            copyright: \"All rights reserved.\",\n            emergencyContact: \"Emergency contact\",\n            businessHours: \"Office hours\",\n            businessHoursTime: \"Mon-Fri: 8:00-18:00\",\n            emergencyAvailable: \"Interventions: 24/7\"\n        }\n    }\n};\nfunction getTranslation(locale, key) {\n    const keys = key.split(\".\");\n    let value = translations[locale];\n    for (const k of keys){\n        value = value?.[k];\n    }\n    return value || key;\n}\nfunction t(key, locale = defaultLocale) {\n    return getTranslation(locale, key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/i18n.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8a9929394fc4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc29sdmljdHVzLXdlYnNpdGUvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzMwZDIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4YTk5MjkzOTRmYzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"],\\\"variable\\\":\\\"--font-poppins\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(rsc)/./src/components/Header.tsx\");\n/* harmony import */ var _components_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Footer */ \"(rsc)/./src/components/Footer.tsx\");\n/* harmony import */ var _components_SEOSchema__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/SEOSchema */ \"(rsc)/./src/components/SEOSchema.tsx\");\n/* harmony import */ var _components_ChatbotWrapper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ChatbotWrapper */ \"(rsc)/./src/components/ChatbotWrapper.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"SOLVICTUS - Profesjonalne sprzątanie po tragedii | Certyfikowana dezynfekcja\",\n    description: \"SOLVICTUS oferuje profesjonalne, certyfikowane sprzątanie i dezynfekcję po zgonach, pożarach, powodziach i skażeniach biologicznych. Zaufanie. Cisza. Skuteczność.\",\n    keywords: \"sprzątanie po zgonach, dezynfekcja po śmierci, sprzątanie po tragedii, ozonowanie, usuwanie zapach\\xf3w, sprzątanie po pożarze, sprzątanie po powodzi, dezynfekcja biologiczna\",\n    authors: [\n        {\n            name: \"SOLVICTUS\"\n        }\n    ],\n    creator: \"SOLVICTUS\",\n    publisher: \"SOLVICTUS\",\n    robots: \"index, follow\",\n    openGraph: {\n        type: \"website\",\n        locale: \"pl_PL\",\n        url: \"https://solvictus.pl\",\n        title: \"SOLVICTUS - Profesjonalne sprzątanie po tragedii\",\n        description: \"Certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach. Pomagamy po tragedii.\",\n        siteName: \"SOLVICTUS\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SOLVICTUS - Profesjonalne sprzątanie po tragedii\",\n        description: \"Certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach.\"\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1,\n    maximumScale: 1,\n    themeColor: \"#0A2144\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pl\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_6___default().variable)} smooth-scroll`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_variable_font_poppins_variableName_poppins___WEBPACK_IMPORTED_MODULE_6___default().className)} antialiased bg-gray-50`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SEOSchema__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        type: \"organization\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatbotWrapper__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/uslugi/usuwanie-skutkow-powodzi/page.tsx":
/*!**********************************************************!*\
  !*** ./src/app/uslugi/usuwanie-skutkow-powodzi/page.tsx ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FloodDamageRemovalPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Droplets,Phone,Thermometer,Wind!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/droplets.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Droplets,Phone,Thermometer,Wind!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Droplets,Phone,Thermometer,Wind!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Droplets,Phone,Thermometer,Wind!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Droplets,Phone,Thermometer,Wind!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/wind.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Droplets,Phone,Thermometer,Wind!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/thermometer.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,Droplets,Phone,Thermometer,Wind!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n\n\n\nconst metadata = {\n    title: \"Usuwanie skutk\\xf3w powodzi - SOLVICTUS | Osuszanie i dezynfekcja\",\n    description: \"Profesjonalne usuwanie skutk\\xf3w powodzi i zalań. Osuszanie, dezynfekcja, zapobieganie pleśni. Szybka reakcja 24/7. Wsp\\xf3łpraca z ubezpieczycielami.\",\n    keywords: \"usuwanie skutk\\xf3w powodzi, sprzątanie po zalaniu, osuszanie po powodzi, dezynfekcja po zalaniu, usuwanie wilgoci, zapobieganie pleśni\"\n};\nfunction FloodDamageRemovalPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-900 via-cyan-800 to-blue-900 text-white py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-2 bg-blue-800 rounded-full px-4 py-2 mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 20,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Specjaliści od powodzi\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold mb-6 leading-tight\",\n                                        children: \"Usuwanie skutk\\xf3w powodzi\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-blue-200 mb-8 leading-relaxed\",\n                                        children: \"Kompleksowe usuwanie skutk\\xf3w powodzi i zalań. Profesjonalne osuszanie, dezynfekcja i zapobieganie pleśni. Przywracamy Tw\\xf3j dom do pełnej sprawności.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"tel:+48123456789\",\n                                                className: \"inline-flex items-center justify-center bg-white text-blue-900 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        className: \"w-5 h-5 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Zadzwoń: +48 123 456 789\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: \"/kontakt\",\n                                                className: \"inline-flex items-center justify-center border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors\",\n                                                children: \"Bezpłatna wycena\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/10 backdrop-blur-sm rounded-2xl p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-6 h-6 text-blue-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 53,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-semibold\",\n                                                    children: \"Reakcja w 2h\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                            lineNumber: 58,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Natychmiastowe osuszanie\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                            lineNumber: 59,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                            lineNumber: 62,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Zapobieganie pleśni\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                            lineNumber: 66,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Profesjonalny sprzęt\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Monitoring wilgotności\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                            lineNumber: 71,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-6\",\n                                    children: \"Kompleksowe usługi po powodzi\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Czas jest kluczowy po powodzi. Im szybciej rozpoczniemy prace, tym mniejsze będą szkody i koszty naprawy.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-xl p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Osuszanie\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Profesjonalne osuszanie pomieszczeń za pomocą przemysłowych osuszaczy i wentylator\\xf3w.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-gray-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Osuszacze kondensacyjne\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Wentylatory przemysłowe\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Monitoring wilgotności\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Kontrola temperatury\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-xl p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Usuwanie wody\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Szybkie usuwanie stojącej wody i wilgoci z wszystkich powierzchni i materiał\\xf3w.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-gray-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Pompy do wody\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Odkurzacze przemysłowe\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Usuwanie z dywan\\xf3w\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Osuszanie ścian\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-xl p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-4\",\n                                            children: \"Zapobieganie pleśni\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Aplikacja środk\\xf3w przeciwgrzybiczych i monitoring warunk\\xf3w zapobiegających rozwojowi pleśni.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-gray-600 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Środki przeciwgrzybicze\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Dezynfekcja powierzchni\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Kontrola wilgotności\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Monitoring długoterminowy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-6\",\n                                children: \"Proces usuwania skutk\\xf3w powodzi\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: \"1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Ocena szk\\xf3d\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Pomiar wilgotności i ocena zakresu zniszczeń\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: \"2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Usuwanie wody\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Wypompowanie stojącej wody i wilgoci\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: \"3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Osuszanie\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Profesjonalne osuszanie wszystkich powierzchni\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Dezynfekcja\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Dezynfekcja i środki przeciwgrzybicze\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: \"5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                            children: \"Monitoring\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Kontrola wilgotności i jakości powietrza\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-2xl p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-16 h-16 text-red-600 mx-auto mb-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-red-900 mb-4\",\n                                    children: \"Czas ma kluczowe znaczenie!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 mb-6\",\n                                    children: \"Po powodzi masz tylko 24-48 godzin na rozpoczęcie profesjonalnego osuszania, aby zapobiec rozwojowi pleśni i minimalizować szkody.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-red-600 mb-1\",\n                                                    children: \"24h\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-red-700\",\n                                                    children: \"Czas na reakcję\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-red-600 mb-1\",\n                                                    children: \"72h\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-red-700\",\n                                                    children: \"Rozw\\xf3j pleśni\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-red-600 mb-1\",\n                                                    children: \"2h\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-red-700\",\n                                                    children: \"Nasz czas reakcji\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-6\",\n                                    children: \"Profesjonalny sprzęt\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Używamy najnowocześniejszego sprzętu przemysłowego do osuszania i monitoringu wilgotności.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 250,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl p-6 shadow-lg text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-gray-900 mb-2\",\n                                            children: \"Osuszacze\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Przemysłowe osuszacze kondensacyjne\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl p-6 shadow-lg text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-gray-900 mb-2\",\n                                            children: \"Mierniki\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Precyzyjne mierniki wilgotności\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl p-6 shadow-lg text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-gray-900 mb-2\",\n                                            children: \"Pompy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Pompy do usuwania wody\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl p-6 shadow-lg text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-orange-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-bold text-gray-900 mb-2\",\n                                            children: \"Wentylatory\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Wentylatory przemysłowe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 border border-blue-200 rounded-2xl p-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-blue-900 mb-4\",\n                                    children: \"Wsp\\xf3łpraca z ubezpieczycielami\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-800 mb-6\",\n                                    children: \"Wsp\\xf3łpracujemy z wszystkimi gł\\xf3wnymi firmami ubezpieczeniowymi w Polsce. Przygotowujemy pełną dokumentację potrzebną do rozliczenia szkody powodziowej.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600 mb-1\",\n                                                    children: \"100%\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Akceptowanych wniosk\\xf3w\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600 mb-1\",\n                                                    children: \"24h\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Przygotowanie dokument\\xf3w\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600 mb-1\",\n                                                    children: \"50+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: \"Firm ubezpieczeniowych\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-blue-900 text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-6\",\n                            children: \"Masz pow\\xf3dź? Zadzwoń natychmiast!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-blue-200 mb-8\",\n                            children: \"Każda minuta zwłoki zwiększa szkody. Jesteśmy dostępni 24/7 i możemy być u Ciebie w ciągu 2 godzin.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"tel:+48123456789\",\n                                    className: \"inline-flex items-center justify-center bg-white text-blue-900 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_Droplets_Phone_Thermometer_Wind_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Zadzwoń: +48 123 456 789\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/kontakt\",\n                                    className: \"inline-flex items-center justify-center border-2 border-white text-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-blue-900 transition-colors\",\n                                    children: \"Napisz do nas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                    lineNumber: 324,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\test2\\\\src\\\\app\\\\uslugi\\\\usuwanie-skutkow-powodzi\\\\page.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/uslugi/usuwanie-skutkow-powodzi/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ChatbotWrapper.tsx":
/*!*******************************************!*\
  !*** ./src/components/ChatbotWrapper.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\test2\src\components\ChatbotWrapper.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Footer.tsx":
/*!***********************************!*\
  !*** ./src/components/Footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\test2\src\components\Footer.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\test2\src\components\Header.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/SEOSchema.tsx":
/*!**************************************!*\
  !*** ./src/components/SEOSchema.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Downloads\test2\src\components\SEOSchema.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&page=%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&appPaths=%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage&pagePath=private-next-app-dir%2Fuslugi%2Fusuwanie-skutkow-powodzi%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmariu%5CDownloads%5Ctest2&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=export&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();