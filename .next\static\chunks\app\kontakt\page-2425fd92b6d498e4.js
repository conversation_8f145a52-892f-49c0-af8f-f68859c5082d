(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[768],{7770:function(e,a,r){Promise.resolve().then(r.bind(r,2190)),Promise.resolve().then(r.bind(r,1146))},9205:function(e,a,r){"use strict";r.d(a,{Z:function(){return m}});var s=r(2265);let t=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,a,r)=>r?r.toUpperCase():a.toLowerCase()),n=e=>{let a=i(e);return a.charAt(0).toUpperCase()+a.slice(1)},l=function(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return a.filter((e,a,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===a).join(" ").trim()},o=e=>{for(let a in e)if(a.startsWith("aria-")||"role"===a||"title"===a)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)((e,a)=>{let{color:r="currentColor",size:t=24,strokeWidth:i=2,absoluteStrokeWidth:n,className:d="",children:m,iconNode:p,...x}=e;return(0,s.createElement)("svg",{ref:a,...c,width:t,height:t,stroke:r,strokeWidth:n?24*Number(i)/Number(t):i,className:l("lucide",d),...!m&&!o(x)&&{"aria-hidden":"true"},...x},[...p.map(e=>{let[a,r]=e;return(0,s.createElement)(a,r)}),...Array.isArray(m)?m:[m]])}),m=(e,a)=>{let r=(0,s.forwardRef)((r,i)=>{let{className:o,...c}=r;return(0,s.createElement)(d,{ref:i,iconNode:a,className:l("lucide-".concat(t(n(e))),"lucide-".concat(e),o),...c})});return r.displayName=n(e),r}},9322:function(e,a,r){"use strict";r.d(a,{Z:function(){return s}});let s=(0,r(9205).Z)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},1671:function(e,a,r){"use strict";r.d(a,{Z:function(){return s}});let s=(0,r(9205).Z)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3774:function(e,a,r){"use strict";r.d(a,{Z:function(){return s}});let s=(0,r(9205).Z)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},3041:function(e,a,r){"use strict";r.d(a,{Z:function(){return s}});let s=(0,r(9205).Z)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},4743:function(e,a,r){"use strict";r.d(a,{Z:function(){return s}});let s=(0,r(9205).Z)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2190:function(e,a,r){"use strict";var s=r(7437),t=r(2265),i=r(1671),n=r(9322),l=r(4743);a.default=()=>{let[e,a]=(0,t.useState)({name:"",email:"",phone:"",subject:"",serviceType:"",urgency:"",message:"",privacy:!1}),[r,o]=(0,t.useState)(!1),[c,d]=(0,t.useState)("idle"),m=e=>{let{name:r,value:s,type:t}=e.target;a(a=>({...a,[r]:"checkbox"===t?e.target.checked:s}))},p=async e=>{e.preventDefault(),o(!0);try{await new Promise(e=>setTimeout(e,2e3)),d("success"),a({name:"",email:"",phone:"",subject:"",serviceType:"",urgency:"",message:"",privacy:!1})}catch(e){d("error")}finally{o(!1)}};return(0,s.jsx)("section",{className:"py-20 bg-white",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Wyślij zapytanie"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Wypełnij formularz, a skontaktujemy się z Tobą w ciągu 24 godzin"})]}),"success"===c&&(0,s.jsxs)("div",{className:"mb-8 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-3",children:[(0,s.jsx)(i.Z,{className:"w-5 h-5 text-green-600"}),(0,s.jsx)("p",{className:"text-green-800",children:"Dziękujemy! Twoja wiadomość została wysłana. Skontaktujemy się z Tobą wkr\xf3tce."})]}),"error"===c&&(0,s.jsxs)("div",{className:"mb-8 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-3",children:[(0,s.jsx)(n.Z,{className:"w-5 h-5 text-red-600"}),(0,s.jsx)("p",{className:"text-red-800",children:"Wystąpił błąd podczas wysyłania wiadomości. Spr\xf3buj ponownie lub zadzwoń do nas."})]}),(0,s.jsxs)("form",{onSubmit:p,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Imię i nazwisko *"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",required:!0,value:e.name,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors",placeholder:"Jan Kowalski"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email *"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",required:!0,value:e.email,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors",placeholder:"<EMAIL>"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Telefon"}),(0,s.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors",placeholder:"+48 123 456 789"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"urgency",className:"block text-sm font-medium text-gray-700 mb-2",children:"Pilność"}),(0,s.jsxs)("select",{id:"urgency",name:"urgency",value:e.urgency,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors",children:[(0,s.jsx)("option",{value:"",children:"Wybierz pilność"}),(0,s.jsx)("option",{value:"emergency",children:"Sytuacja kryzysowa (natychmiast)"}),(0,s.jsx)("option",{value:"urgent",children:"Pilne (w ciągu 24h)"}),(0,s.jsx)("option",{value:"normal",children:"Normalne (w ciągu 2-3 dni)"}),(0,s.jsx)("option",{value:"planned",children:"Planowane (elastyczny termin)"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"serviceType",className:"block text-sm font-medium text-gray-700 mb-2",children:"Rodzaj usługi"}),(0,s.jsxs)("select",{id:"serviceType",name:"serviceType",value:e.serviceType,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors",children:[(0,s.jsx)("option",{value:"",children:"Wybierz rodzaj usługi"}),(0,s.jsx)("option",{value:"death-cleanup",children:"Sprzątanie po zgonie"}),(0,s.jsx)("option",{value:"fire-cleanup",children:"Sprzątanie po pożarze"}),(0,s.jsx)("option",{value:"flood-cleanup",children:"Usuwanie skutk\xf3w powodzi"}),(0,s.jsx)("option",{value:"biological-decontamination",children:"Dezynfekcja biologiczna"}),(0,s.jsx)("option",{value:"ozone-treatment",children:"Ozonowanie pomieszczeń"}),(0,s.jsx)("option",{value:"business-services",children:"Usługi dla firm"}),(0,s.jsx)("option",{value:"consultation",children:"Konsultacja"}),(0,s.jsx)("option",{value:"other",children:"Inne"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:"Temat"}),(0,s.jsx)("input",{type:"text",id:"subject",name:"subject",value:e.subject,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors",placeholder:"Kr\xf3tki opis sytuacji"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Wiadomość *"}),(0,s.jsx)("textarea",{id:"message",name:"message",required:!0,rows:6,value:e.message,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors resize-vertical",placeholder:"Opisz szczeg\xf3łowo swoją sytuację. Im więcej informacji podasz, tym lepiej będziemy mogli Ci pom\xf3c."})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("input",{type:"checkbox",id:"privacy",name:"privacy",required:!0,checked:e.privacy,onChange:m,className:"mt-1 w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"}),(0,s.jsxs)("label",{htmlFor:"privacy",className:"text-sm text-gray-600",children:["Wyrażam zgodę na przetwarzanie moich danych osobowych zgodnie z"," ",(0,s.jsx)("a",{href:"/polityka-prywatnosci",className:"text-primary-600 hover:text-primary-700 underline",children:"Polityką Prywatności"}),". *"]})]}),(0,s.jsx)("button",{type:"submit",disabled:r,className:"w-full bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors flex items-center justify-center space-x-2",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),(0,s.jsx)("span",{children:"Wysyłanie..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.Z,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Wyślij wiadomość"})]})})]}),(0,s.jsx)("div",{className:"mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,s.jsxs)("p",{className:"text-yellow-800 text-sm",children:[(0,s.jsx)("strong",{children:"Sytuacja kryzysowa?"})," W pilnych przypadkach zadzwoń bezpośrednio pod numer"," ",(0,s.jsx)("a",{href:"tel:+48123456789",className:"font-semibold underline",children:"+48 123 456 789"})]})})]})})}},1146:function(e,a,r){"use strict";r.d(a,{default:function(){return o}});var s=r(7437),t=r(2265),i=r(3774);let n=(0,r(9205).Z)("navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);var l=r(3041),o=()=>{let e=(0,t.useRef)(null),a=(0,t.useRef)(null),r=(0,t.useMemo)(()=>({lat:52.2297,lng:21.0122,address:"ul. Przykładowa 123, 00-001 Warszawa"}),[]);(0,t.useEffect)(()=>{let s=()=>{if(!e.current||!window.google)return;let s=new window.google.maps.Map(e.current,{center:r,zoom:15,styles:[{featureType:"all",elementType:"geometry.fill",stylers:[{color:"#f8f9fa"}]},{featureType:"water",elementType:"geometry",stylers:[{color:"#3A7DFF"}]},{featureType:"road",elementType:"geometry",stylers:[{color:"#ffffff"}]}]}),t=new window.google.maps.Marker({position:r,map:s,title:"SOLVICTUS Sp. z o.o.",icon:{url:"data:image/svg+xml;charset=UTF-8,"+encodeURIComponent('\n            <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">\n              <circle cx="20" cy="20" r="18" fill="#0A2144" stroke="#ffffff" stroke-width="2"/>\n              <circle cx="20" cy="20" r="8" fill="#ffffff"/>\n              <text x="20" y="25" text-anchor="middle" fill="#0A2144" font-size="10" font-weight="bold">S</text>\n            </svg>\n          '),scaledSize:new window.google.maps.Size(40,40)}}),i=new window.google.maps.InfoWindow({content:'\n          <div style="padding: 10px; max-width: 250px;">\n            <h3 style="margin: 0 0 8px 0; color: #0A2144; font-size: 16px; font-weight: bold;">SOLVICTUS Sp. z o.o.</h3>\n            <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">'.concat(r.address,'</p>\n            <p style="margin: 0 0 8px 0; color: #666; font-size: 12px;">Profesjonalne sprzątanie po tragedii</p>\n            <div style="display: flex; gap: 8px; margin-top: 10px;">\n              <a href="tel:+48123456789" style="background: #0A2144; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;">Zadzwoń</a>\n              <a href="https://www.google.com/maps/dir/?api=1&destination=').concat(r.lat,",").concat(r.lng,'" target="_blank" style="background: #3A7DFF; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;">Trasa</a>\n            </div>\n          </div>\n        ')});t.addListener("click",()=>{i.open(s,t)}),a.current=s};return(()=>{if(window.google&&window.google.maps){s();return}let e=document.createElement("script");e.src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap",e.async=!0,e.defer=!0,window.initMap=s,document.head.appendChild(e)})(),()=>{window.initMap&&delete window.initMap}},[r]);let o=()=>{let e="https://www.google.com/maps/dir/?api=1&destination=".concat(r.lat,",").concat(r.lng);window.open(e,"_blank")};return(0,s.jsx)("section",{className:"py-20 bg-white",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Nasza lokalizacja"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Nasze biuro znajduje się w centrum Warszawy. Działamy jednak w całej Polsce, zapewniając szybką reakcję w sytuacjach kryzysowych."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2",children:[(0,s.jsxs)("div",{className:"bg-gray-200 rounded-xl overflow-hidden shadow-lg h-96 lg:h-[500px] relative",children:[(0,s.jsx)("div",{ref:e,className:"w-full h-full",style:{minHeight:"400px"}}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gradient-to-br from-primary-100 to-primary-200",id:"map-loading",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(i.Z,{className:"w-16 h-16 text-primary-600 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-bold text-primary-900 mb-2",children:"Ładowanie mapy..."}),(0,s.jsxs)("p",{className:"text-primary-700 mb-4",children:["ul. Przykładowa 123",(0,s.jsx)("br",{}),"00-001 Warszawa"]}),(0,s.jsxs)("button",{onClick:o,className:"bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2 mx-auto",children:[(0,s.jsx)(n,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Wyznacz trasę"})]})]})}),(0,s.jsx)("div",{className:"absolute top-4 right-4 bg-white rounded-lg shadow-lg p-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,s.jsx)("div",{className:"w-3 h-3 bg-red-500 rounded-full animate-pulse"}),(0,s.jsx)("span",{children:"Biuro gł\xf3wne"})]})}),(0,s.jsxs)("div",{className:"absolute bottom-4 left-4 flex space-x-2",children:[(0,s.jsxs)("button",{onClick:o,className:"bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg shadow-lg text-sm font-medium transition-colors flex items-center space-x-2",children:[(0,s.jsx)(n,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Trasa"})]}),(0,s.jsxs)("a",{href:"tel:+48123456789",className:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg shadow-lg text-sm font-medium transition-colors flex items-center space-x-2",children:[(0,s.jsx)(l.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Zadzwoń"})]})]})]}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Kliknij marker na mapie, aby zobaczyć szczeg\xf3ły i opcje nawigacji"})})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,s.jsx)(i.Z,{className:"w-6 h-6 text-primary-600"}),(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900",children:"Adres biura"})]}),(0,s.jsxs)("div",{className:"space-y-2 text-gray-600",children:[(0,s.jsx)("p",{children:"SOLVICTUS Sp. z o.o."}),(0,s.jsx)("p",{children:"ul. Przykładowa 123"}),(0,s.jsx)("p",{children:"00-001 Warszawa"}),(0,s.jsx)("p",{children:"Polska"})]}),(0,s.jsxs)("button",{onClick:o,className:"mt-4 w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2",children:[(0,s.jsx)(n,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Wyznacz trasę"})]})]}),(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,s.jsx)(l.Z,{className:"w-6 h-6 text-red-600"}),(0,s.jsx)("h3",{className:"text-lg font-bold text-red-900",children:"Sytuacja kryzysowa?"})]}),(0,s.jsx)("p",{className:"text-red-800 mb-4",children:"Nie przyjeżdżaj do biura. Zadzwoń natychmiast, a nasz zesp\xf3ł przyjedzie do Ciebie."}),(0,s.jsx)("a",{href:"tel:+48123456789",className:"block w-full bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg font-bold text-center transition-colors",children:"+48 123 456 789"})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-xl p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-gray-900 mb-4",children:"Dojazd komunikacją"}),(0,s.jsxs)("div",{className:"space-y-3 text-sm text-gray-600",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Metro:"}),(0,s.jsx)("p",{children:"Stacja Centrum (linia M1) - 5 min pieszo"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Autobus:"}),(0,s.jsx)("p",{children:"Linie: 175, 180, 503 - przystanek Plac Konstytucji"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Tramwaj:"}),(0,s.jsx)("p",{children:"Linie: 4, 15, 18, 35 - przystanek Nowy Świat"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium",children:"Parking:"}),(0,s.jsx)("p",{children:"Płatne miejsca parkingowe w strefie płatnego parkowania"})]})]})]}),(0,s.jsxs)("div",{className:"bg-primary-50 border border-primary-200 rounded-xl p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-primary-900 mb-4",children:"Zasięg działania"}),(0,s.jsx)("p",{className:"text-primary-800 text-sm mb-3",children:"Choć nasze biuro znajduje się w Warszawie, działamy w całej Polsce:"}),(0,s.jsxs)("ul",{className:"space-y-1 text-sm text-primary-700",children:[(0,s.jsx)("li",{children:"• Warszawa i okolice - do 2h"}),(0,s.jsx)("li",{children:"• Gł\xf3wne miasta - do 4h"}),(0,s.jsx)("li",{children:"• Cała Polska - do 8h"}),(0,s.jsx)("li",{children:"• Sytuacje kryzysowe - priorytet"})]})]})]})]})]})})}}},function(e){e.O(0,[971,117,744],function(){return e(e.s=7770)}),_N_E=e.O()}]);