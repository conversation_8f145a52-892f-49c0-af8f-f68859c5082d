(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{359:function(e,t,n){Promise.resolve().then(n.t.bind(n,9759,23)),Promise.resolve().then(n.t.bind(n,2778,23)),Promise.resolve().then(n.bind(n,2988))},8221:function(e,t){"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DOMAttributeNames:function(){return r},default:function(){return i},isEqualNode:function(){return o}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function a(e){let{type:t,props:n}=e,a=document.createElement(t);for(let e in n){if(!n.hasOwnProperty(e)||"children"===e||"dangerouslySetInnerHTML"===e||void 0===n[e])continue;let o=r[e]||e.toLowerCase();"script"===t&&("async"===o||"defer"===o||"noModule"===o)?a[o]=!!n[e]:a.setAttribute(o,n[e])}let{children:o,dangerouslySetInnerHTML:i}=n;return i?a.innerHTML=i.__html||"":o&&(a.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):""),a}function o(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let n=t.getAttribute("nonce");if(n&&!e.getAttribute("nonce")){let r=t.cloneNode(!0);return r.setAttribute("nonce",""),r.nonce=n,n===e.nonce&&e.isEqualNode(r)}}return e.isEqualNode(t)}function i(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;e.props.href=e.props["data-href"],e.props["data-href"]=void 0}let n=t[e.type]||[];n.push(e),t[e.type]=n});let r=t.title?t.title[0]:null,a="";if(r){let{children:e}=r.props;a="string"==typeof e?e:Array.isArray(e)?e.join(""):""}a!==document.title&&(document.title=a),["meta","base","link","style","script"].forEach(e=>{n(e,t[e]||[])})}}}n=(e,t)=>{let n=document.getElementsByTagName("head")[0],r=n.querySelector("meta[name=next-head-count]"),i=Number(r.content),l=[];for(let t=0,n=r.previousElementSibling;t<i;t++,n=(null==n?void 0:n.previousElementSibling)||null){var s;(null==n?void 0:null==(s=n.tagName)?void 0:s.toLowerCase())===e&&l.push(n)}let u=t.map(a).filter(e=>{for(let t=0,n=l.length;t<n;t++)if(o(l[t],e))return l.splice(t,1),!1;return!0});l.forEach(e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),u.forEach(e=>n.insertBefore(e,r)),r.content=(i-l.length+u.length).toString()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3515:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8003:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return v},handleClientScriptLoad:function(){return h},initScriptLoader:function(){return m}});let r=n(7043),a=n(3099),o=n(7437),i=r._(n(4887)),l=a._(n(2265)),s=n(8701),u=n(8221),c=n(3515),d=new Map,f=new Set,p=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"],y=e=>{if(i.default.preinit){e.forEach(e=>{i.default.preinit(e,{as:"style"})});return}if("undefined"!=typeof window){let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}},g=e=>{let{src:t,id:n,onLoad:r=()=>{},onReady:a=null,dangerouslySetInnerHTML:o,children:i="",strategy:l="afterInteractive",onError:s,stylesheets:c}=e,g=n||t;if(g&&f.has(g))return;if(d.has(t)){f.add(g),d.get(t).then(r,s);return}let h=()=>{a&&a(),f.add(g)},m=document.createElement("script"),b=new Promise((e,t)=>{m.addEventListener("load",function(t){e(),r&&r.call(this,t),h()}),m.addEventListener("error",function(e){t(e)})}).catch(function(e){s&&s(e)});for(let[n,r]of(o?(m.innerHTML=o.__html||"",h()):i?(m.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",h()):t&&(m.src=t,d.set(t,b)),Object.entries(e))){if(void 0===r||p.includes(n))continue;let e=u.DOMAttributeNames[n]||n.toLowerCase();m.setAttribute(e,r)}"worker"===l&&m.setAttribute("type","text/partytown"),m.setAttribute("data-nscript",l),c&&y(c),document.body.appendChild(m)};function h(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>g(e))}):g(e)}function m(e){e.forEach(h),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function b(e){let{id:t,src:n="",onLoad:r=()=>{},onReady:a=null,strategy:u="afterInteractive",onError:d,stylesheets:p,...y}=e,{updateScripts:h,scripts:m,getIsSsr:b,appDir:v,nonce:_}=(0,l.useContext)(s.HeadManagerContext),C=(0,l.useRef)(!1);(0,l.useEffect)(()=>{let e=t||n;C.current||(a&&e&&f.has(e)&&a(),C.current=!0)},[a,t,n]);let O=(0,l.useRef)(!1);if((0,l.useEffect)(()=>{!O.current&&("afterInteractive"===u?g(e):"lazyOnload"===u&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>g(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>g(e))})),O.current=!0)},[e,u]),("beforeInteractive"===u||"worker"===u)&&(h?(m[u]=(m[u]||[]).concat([{id:t,src:n,onLoad:r,onReady:a,onError:d,...y}]),h(m)):b&&b()?f.add(t||n):b&&!b()&&g(e)),v){if(p&&p.forEach(e=>{i.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)return n?(i.default.preload(n,y.integrity?{as:"script",integrity:y.integrity,nonce:_,crossOrigin:y.crossOrigin}:{as:"script",nonce:_,crossOrigin:y.crossOrigin}),(0,o.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{...y,id:t}])+")"}})):(y.dangerouslySetInnerHTML&&(y.children=y.dangerouslySetInnerHTML.__html,delete y.dangerouslySetInnerHTML),(0,o.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...y,id:t}])+")"}}));"afterInteractive"===u&&n&&i.default.preload(n,y.integrity?{as:"script",integrity:y.integrity,nonce:_,crossOrigin:y.crossOrigin}:{as:"script",nonce:_,crossOrigin:y.crossOrigin})}return null}Object.defineProperty(b,"__nextScript",{value:!0});let v=b;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2988:function(e,t,n){"use strict";n.d(t,{default:function(){return i}});var r=n(7437),a=n(8003),o=n.n(a),i=e=>{let{type:t="organization",title:n,description:a,url:i}=e,l={"@context":"https://schema.org","@type":"Organization",name:"SOLVICTUS",description:"Profesjonalne, certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach",url:"https://solvictus.pl",logo:"https://solvictus.pl/logo.png",contactPoint:{"@type":"ContactPoint",telephone:"+**************",contactType:"emergency",availableLanguage:"Polish",hoursAvailable:"24/7"},address:{"@type":"PostalAddress",streetAddress:"ul. Przykładowa 123",addressLocality:"Warszawa",postalCode:"00-001",addressCountry:"PL"},areaServed:{"@type":"Country",name:"Poland"},serviceType:["Sprzątanie po zgonach","Dezynfekcja po śmierci","Sprzątanie po pożarach","Usuwanie skutk\xf3w powodzi","Ozonowanie pomieszczeń","Dezynfekcja biologiczna"],hasCredential:[{"@type":"EducationalOccupationalCredential",name:"Certyfikat PZH",credentialCategory:"Dezynfekcja"},{"@type":"EducationalOccupationalCredential",name:"ISO 9001:2015",credentialCategory:"Zarządzanie jakością"}]},s={"@context":"https://schema.org","@type":"Service",name:n||"Profesjonalne sprzątanie po tragedii",description:a||"Certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach",provider:{"@type":"Organization",name:"SOLVICTUS",url:"https://solvictus.pl"},areaServed:{"@type":"Country",name:"Poland"},availableChannel:{"@type":"ServiceChannel",servicePhone:"+**************",serviceUrl:"https://solvictus.pl/kontakt"},hoursAvailable:"24/7",category:"Cleaning Services"},u={"@context":"https://schema.org","@type":"LocalBusiness",name:"SOLVICTUS",image:"https://solvictus.pl/logo.png",telephone:"+**************",email:"<EMAIL>",address:{"@type":"PostalAddress",streetAddress:"ul. Przykładowa 123",addressLocality:"Warszawa",postalCode:"00-001",addressCountry:"PL"},geo:{"@type":"GeoCoordinates",latitude:52.2297,longitude:21.0122},url:"https://solvictus.pl",openingHours:"Mo-Su 00:00-23:59",priceRange:"$$",aggregateRating:{"@type":"AggregateRating",ratingValue:"4.9",reviewCount:"127"}};return(0,r.jsx)(o(),{id:"structured-data",type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify((()=>{switch(t){case"service":return s;case"organization":return[l,u];default:return l}})())}})}},2778:function(){},9759:function(e){e.exports={style:{fontFamily:"'__Poppins_51684b', '__Poppins_Fallback_51684b'",fontStyle:"normal"},className:"__className_51684b",variable:"__variable_51684b"}}},function(e){e.O(0,[875,971,117,744],function(){return e(e.s=359)}),_N_E=e.O()}]);