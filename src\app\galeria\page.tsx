'use client'

import { useState } from 'react'
import GalleryHero from '@/components/gallery/GalleryHero'
import GalleryFilters from '@/components/gallery/GalleryFilters'
import GalleryGrid from '@/components/gallery/GalleryGrid'
import GalleryStats from '@/components/gallery/GalleryStats'

export default function GalleryPage() {
  const [activeFilter, setActiveFilter] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'masonry'>('grid')

  return (
    <>
      <GalleryHero />
      <GalleryStats />
      {/* <GalleryFilters
        activeFilter={activeFilter}
        setActiveFilter={setActiveFilter}
        viewMode={viewMode}
        setViewMode={setViewMode}
      /> */}
      <GalleryGrid
        activeFilter="all"
        viewMode="grid"
      />
    </>
  )
}
