import type { Metadata } from 'next'
import { Poppins } from 'next/font/google'
import './globals.css'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import SEOSchema from '@/components/SEOSchema'
import ChatbotWrapper from '@/components/ChatbotWrapper'

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-poppins',
})

export const metadata: Metadata = {
  title: 'SOLVICTUS - Profesjonalne sprzątanie po tragedii | Certyfikowana dezynfekcja',
  description: 'SOLVICTUS oferuje profesjonalne, certyfikowane sprzątanie i dezynfekcję po zgonach, pożarach, powodziach i skażeniach biologicznych. Zaufanie. Cisza. Skuteczność.',
  keywords: 'sprzątanie po z<PERSON>, dezyn<PERSON><PERSON><PERSON><PERSON> po śmierci, sprzątanie po tragedii, ozonowanie, usuwanie zapachów, sprzątanie po pożarze, sprzątanie po powodzi, dezynfekcja biologiczna',
  authors: [{ name: 'SOLVICTUS' }],
  creator: 'SOLVICTUS',
  publisher: 'SOLVICTUS',
  robots: 'index, follow',
  openGraph: {
    type: 'website',
    locale: 'pl_PL',
    url: 'https://solvictus.pl',
    title: 'SOLVICTUS - Profesjonalne sprzątanie po tragedii',
    description: 'Certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach. Pomagamy po tragedii.',
    siteName: 'SOLVICTUS',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SOLVICTUS - Profesjonalne sprzątanie po tragedii',
    description: 'Certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach.',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: '#0A2144',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="pl" className={`${poppins.variable} smooth-scroll`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
      </head>
      <body className={`${poppins.className} antialiased bg-gray-50`}>
        <SEOSchema type="organization" />
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
        {/* <ChatbotWrapper /> */}
      </body>
    </html>
  )
}
