import { Metadata } from 'next'
import PricingHero from '@/components/pricing/PricingHero'
import PricingCalculator from '@/components/pricing/PricingCalculator'
import PricingFactors from '@/components/pricing/PricingFactors'
import PricingFAQ from '@/components/pricing/PricingFAQ'

export const metadata: Metadata = {
  title: 'Cennik usług - SOLVICTUS | Przejrzyste ceny sprzątania po tragedii',
  description: 'Sprawdź nasze ceny za sprzątanie po zgonach, pożarach, powodziach. Kalkulator kosztów online, bezpłatna wycena na miejscu. Przejrzyste ceny bez ukrytych kosztów.',
  keywords: 'cennik SOLVICTUS, ceny sprzątania, koszt dezynfekcji, wycena sprzątania po zgonie, ceny ozonowania',
}

export default function PricingPage() {
  return (
    <>
      <PricingHero />
      <PricingCalculator />
      <PricingFactors />
      <PricingFAQ />
    </>
  )
}
