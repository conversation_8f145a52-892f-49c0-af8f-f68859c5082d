import { Shield, Download, Award, CheckCircle, FileText } from 'lucide-react'

const CertificationsSection = () => {
  const certifications = [
    {
      title: 'Certyfikat PZH',
      description: 'Państwowy Zakład Higieny - certyfikat uprawniający do wykonywania dezynfekcji',
      number: 'PZH/2024/001',
      validUntil: '2025-12-31',
      downloadUrl: '/certificates/pzh-certificate.pdf'
    },
    {
      title: 'ISO 9001:2015',
      description: 'Międzynarodowy standard zarządzania jakością',
      number: 'ISO-9001-2024-SOLV',
      validUntil: '2027-06-15',
      downloadUrl: '/certificates/iso-9001.pdf'
    },
    {
      title: 'Licencja na odpady medyczne',
      description: 'Uprawnienia do transportu i utylizacji odpadów niebezpiecznych',
      number: 'LOM/2024/789',
      validUntil: '2026-03-20',
      downloadUrl: '/certificates/medical-waste.pdf'
    },
    {
      title: 'Certyfikat BHP',
      description: 'Bezpieczeństwo i higiena pracy w środowisku zagrożeń biologicznych',
      number: 'BHP/BIO/2024/156',
      validUntil: '2025-09-10',
      downloadUrl: '/certificates/bhp-certificate.pdf'
    }
  ]

  const standards = [
    'EN 14885 - Dezynfekcja chemiczna',
    'EN 1276 - Działanie bakteriobójcze',
    'EN 1650 - Działanie grzybobójcze',
    'EN 14476 - Działanie wirusobójcze',
    'HACCP - Analiza zagrożeń',
    'GMP - Dobra praktyka produkcyjna'
  ]

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-green-50 rounded-full px-4 py-2 mb-6">
            <Award className="w-5 h-5 text-green-600" />
            <span className="text-green-700 text-sm font-medium">Certyfikacje i standardy</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Gwarancja najwyższej
            <span className="block gradient-text">jakości usług</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Posiadamy wszystkie wymagane certyfikaty i licencje. Nasze procedury są zgodne 
            z najwyższymi standardami międzynarodowymi i krajowymi.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Certifications */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-8 flex items-center space-x-3">
              <Shield className="w-6 h-6 text-primary-600" />
              <span>Nasze certyfikaty</span>
            </h3>
            
            <div className="space-y-6">
              {certifications.map((cert, index) => (
                <div
                  key={index}
                  className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-gray-900 mb-2">
                        {cert.title}
                      </h4>
                      <p className="text-gray-600 text-sm mb-3">
                        {cert.description}
                      </p>
                      <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-1 sm:space-y-0 text-sm text-gray-500">
                        <span>Nr: {cert.number}</span>
                        <span>Ważny do: {cert.validUntil}</span>
                      </div>
                    </div>
                    <a
                      href={cert.downloadUrl}
                      className="ml-4 flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                      download
                    >
                      <Download className="w-4 h-4" />
                      <span>Pobierz</span>
                    </a>
                  </div>
                  <div className="flex items-center space-x-2 text-green-600">
                    <CheckCircle className="w-4 h-4" />
                    <span className="text-sm font-medium">Aktywny certyfikat</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Standards */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-8 flex items-center space-x-3">
              <FileText className="w-6 h-6 text-primary-600" />
              <span>Standardy i normy</span>
            </h3>
            
            <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-lg p-8">
              <p className="text-gray-700 mb-6 leading-relaxed">
                Nasze procedury są opracowane zgodnie z najwyższymi standardami branżowymi 
                i regularnie aktualizowane zgodnie z najnowszymi wytycznymi.
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {standards.map((standard, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-3 bg-white rounded-lg p-3"
                  >
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-sm font-medium text-gray-700">
                      {standard}
                    </span>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-white rounded-lg border-l-4 border-primary-500">
                <div className="flex items-start space-x-3">
                  <Award className="w-5 h-5 text-primary-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">
                      Ciągłe doskonalenie
                    </h4>
                    <p className="text-sm text-gray-600">
                      Regularnie uczestniczymy w szkoleniach i aktualizujemy nasze procedury 
                      zgodnie z najnowszymi standardami branżowymi.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Trust indicators */}
        <div className="bg-gray-50 rounded-2xl p-8 md:p-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Zaufali nam
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Współpracujemy z instytucjami publicznymi, firmami ubezpieczeniowymi 
              i prywatnymi klientami w całej Polsce.
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center opacity-60">
            <div className="text-center">
              <div className="text-lg font-bold text-gray-700">Ministerstwo Zdrowia</div>
              <div className="text-sm text-gray-500">Partner strategiczny</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-gray-700">PZU</div>
              <div className="text-sm text-gray-500">Rekomendowany wykonawca</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-gray-700">Warta</div>
              <div className="text-sm text-gray-500">Certyfikowany partner</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-gray-700">Straż Pożarna</div>
              <div className="text-sm text-gray-500">Współpraca operacyjna</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default CertificationsSection
