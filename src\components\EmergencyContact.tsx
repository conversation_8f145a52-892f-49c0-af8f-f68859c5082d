import { Phone, Clock, MapPin, AlertTriangle } from 'lucide-react'

const EmergencyContact = () => {
  return (
    <section className="py-20 bg-red-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-gradient-to-r from-red-600 to-red-700 rounded-2xl p-8 md:p-12 text-white relative overflow-hidden">
          {/* Background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
            }}></div>
          </div>

          <div className="relative z-10">
            <div className="text-center mb-12">
              <div className="inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
                <AlertTriangle className="w-5 h-5 text-white" />
                <span className="text-white text-sm font-medium">Sytuacja kryzysowa</span>
              </div>
              
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">
                Potrzebujesz natychmiastowej pomocy?
              </h2>
              
              <p className="text-xl text-red-100 mb-8 max-w-3xl mx-auto leading-relaxed">
                Rozumiemy, że sytuacje kryzysowe wymagają szybkiej reakcji. 
                Nasz zespół jest dostępny 24 godziny na dobę, 7 dni w tygodniu.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Contact info */}
              <div className="space-y-8">
                <div className="text-center lg:text-left">
                  <h3 className="text-2xl font-bold mb-6">Zadzwoń teraz</h3>
                  
                  <a 
                    href="tel:+48123456789"
                    className="inline-block bg-white text-red-600 px-8 py-4 rounded-xl font-bold text-2xl md:text-3xl hover:bg-gray-100 transition-colors shadow-lg hover:shadow-xl transform hover:-translate-y-1 duration-200"
                  >
                    +48 123 456 789
                  </a>
                  
                  <p className="text-red-100 mt-4 text-sm">
                    Natychmiastowa pomoc • Bezpłatna konsultacja • Dyskretne działanie
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
                  <div className="text-center lg:text-left">
                    <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto lg:mx-0 mb-3">
                      <Clock className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="font-semibold mb-2">24/7 Dostępność</h4>
                    <p className="text-red-100 text-sm">Całodobowa linia pomocy</p>
                  </div>
                  
                  <div className="text-center lg:text-left">
                    <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto lg:mx-0 mb-3">
                      <MapPin className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="font-semibold mb-2">Cała Polska</h4>
                    <p className="text-red-100 text-sm">Działamy w całym kraju</p>
                  </div>
                  
                  <div className="text-center lg:text-left">
                    <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto lg:mx-0 mb-3">
                      <Phone className="w-6 h-6 text-white" />
                    </div>
                    <h4 className="font-semibold mb-2">Szybka reakcja</h4>
                    <p className="text-red-100 text-sm">Średnio 2 godziny</p>
                  </div>
                </div>
              </div>

              {/* Emergency steps */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8">
                <h3 className="text-xl font-bold mb-6 text-center lg:text-left">
                  Co robimy w sytuacji kryzysowej?
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-white text-red-600 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                      1
                    </div>
                    <div>
                      <h4 className="font-semibold mb-1">Natychmiastowy kontakt</h4>
                      <p className="text-red-100 text-sm">Odbieramy telefon i oceniamy sytuację</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-white text-red-600 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                      2
                    </div>
                    <div>
                      <h4 className="font-semibold mb-1">Szybki dojazd</h4>
                      <p className="text-red-100 text-sm">Zespół wyrusza w ciągu 2 godzin</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-white text-red-600 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                      3
                    </div>
                    <div>
                      <h4 className="font-semibold mb-1">Profesjonalne działanie</h4>
                      <p className="text-red-100 text-sm">Dyskretne i skuteczne rozwiązanie problemu</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-4">
                    <div className="w-8 h-8 bg-white text-red-600 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                      4
                    </div>
                    <div>
                      <h4 className="font-semibold mb-1">Wsparcie po usłudze</h4>
                      <p className="text-red-100 text-sm">Pomoc w załatwieniu formalności</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional contact methods */}
            <div className="mt-12 text-center">
              <h3 className="text-xl font-bold mb-6">Inne sposoby kontaktu</h3>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="mailto:<EMAIL>"
                  className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-colors backdrop-blur-sm"
                >
                  <EMAIL>
                </a>
                <a
                  href="/kontakt"
                  className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-colors backdrop-blur-sm"
                >
                  Formularz kontaktowy
                </a>
              </div>
              <p className="text-red-100 text-sm mt-4">
                W sytuacjach pilnych zalecamy kontakt telefoniczny
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default EmergencyContact
