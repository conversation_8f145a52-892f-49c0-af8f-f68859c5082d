'use client'

import Link from 'next/link'
import { Phone, Shield, Clock, CheckCircle } from 'lucide-react'

const HeroSection = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 hero-gradient"></div>
      
      {/* Background pattern overlay */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          {/* Main heading */}
          <div className="mb-8">
            <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
              <Shield className="w-5 h-5 text-white" />
              <span className="text-white text-sm font-medium">Certyfikowana dezynfekcja</span>
            </div>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              Pomagamy po
              <span className="block text-primary-300">tragedii</span>
            </h1>
            <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto leading-relaxed">
              Profesjonalne, certyfikowane sprzątanie i dezynfekcja po traumatycznych wydarzeniach. 
              <span className="block mt-2 font-medium text-primary-200">&quot;Zaufanie. Cisza. Skuteczność.&quot;</span>
            </p>
          </div>

          {/* Key features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Clock className="w-8 h-8 text-primary-300 mx-auto mb-3" />
              <h3 className="text-white font-semibold mb-2">24/7 Dostępność</h3>
              <p className="text-gray-200 text-sm">Natychmiastowa reakcja w sytuacjach kryzysowych</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Shield className="w-8 h-8 text-primary-300 mx-auto mb-3" />
              <h3 className="text-white font-semibold mb-2">Certyfikowani</h3>
              <p className="text-gray-200 text-sm">Licencjonowani specjaliści z wieloletnim doświadczeniem</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <CheckCircle className="w-8 h-8 text-primary-300 mx-auto mb-3" />
              <h3 className="text-white font-semibold mb-2">Dyskrecja</h3>
              <p className="text-gray-200 text-sm">Pełna poufność i empatyczne podejście</p>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link
              href="/kontakt"
              className="bg-white text-primary-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-all duration-200 hover:shadow-xl transform hover:-translate-y-1 flex items-center space-x-2"
            >
              <Phone className="w-5 h-5" />
              <span>Zgłoś sytuację</span>
            </Link>
            <Link
              href="/uslugi"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-900 transition-all duration-200 hover:shadow-xl transform hover:-translate-y-1"
            >
              Zobacz usługi
            </Link>
          </div>

          {/* Emergency contact */}
          <div className="bg-red-600/20 backdrop-blur-sm border border-red-400/30 rounded-lg p-6 max-w-2xl mx-auto">
            <div className="flex items-center justify-center space-x-3 mb-3">
              <Phone className="w-6 h-6 text-red-300" />
              <span className="text-red-200 font-medium">Linia kryzysowa 24/7</span>
            </div>
            <a 
              href="tel:+48123456789" 
              className="text-2xl md:text-3xl font-bold text-white hover:text-red-200 transition-colors"
            >
              +48 123 456 789
            </a>
            <p className="text-red-200 text-sm mt-2">Natychmiastowa pomoc w sytuacjach kryzysowych</p>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  )
}

export default HeroSection
