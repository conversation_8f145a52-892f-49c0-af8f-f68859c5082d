import { Star, Quote } from 'lucide-react'

const TestimonialsPreview = () => {
  const testimonials = [
    {
      name: '<PERSON>',
      location: 'Warszawa',
      rating: 5,
      text: 'W najtrudniejszej chwili mojego życia zespół SOLVICTUS okazał się niezwykle profesjonalny i empatyczny. Wszystko zostało wykonane dyskretnie i z najwyższą starannością. Jestem bardzo wdzięczna za ich pomoc.',
      service: 'Sprzątanie po zgonie'
    },
    {
      name: '<PERSON><PERSON>',
      location: 'Kraków',
      rating: 5,
      text: 'Po pożarze w naszym domu myśleliśmy, że wszystko stracone. SOLVICTUS nie tylko profesjonalnie usunął skutki pożaru, ale także pomógł nam odzyskać nadzieję. Polecam z całego serca.',
      service: 'Sprzątanie po pożarze'
    },
    {
      name: 'Firma ABC Sp. z o.o.',
      location: 'Gdańsk',
      rating: 5,
      text: 'Współpracujemy z SOLVICTUS od 3 lat. Ich profesjonalizm, punktualność i jakość usług są na najwyższym poziomie. To partner, na którego zawsze możemy liczyć.',
      service: 'Usługi dla firm'
    }
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-yellow-50 rounded-full px-4 py-2 mb-6">
            <Star className="w-5 h-5 text-yellow-600" />
            <span className="text-yellow-700 text-sm font-medium">Opinie klientów</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Zaufanie potwierdzone
            <span className="block gradient-text">opiniami</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Najlepszym dowodem na jakość naszych usług są opinie zadowolonych klientów. 
            Każda pozytywna recenzja motywuje nas do dalszego doskonalenia.
          </p>
        </div>

        {/* Testimonials grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 card-hover relative"
            >
              {/* Quote icon */}
              <div className="absolute top-6 right-6 opacity-10">
                <Quote className="w-12 h-12 text-primary-600" />
              </div>
              
              {/* Rating */}
              <div className="flex items-center space-x-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              
              {/* Testimonial text */}
              <p className="text-gray-700 leading-relaxed mb-6 relative z-10">
                &quot;{testimonial.text}&quot;
              </p>
              
              {/* Author info */}
              <div className="border-t border-gray-100 pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {testimonial.location}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-primary-600 font-medium">
                      {testimonial.service}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Stats section */}
        <div className="bg-white rounded-2xl p-8 md:p-12 shadow-lg">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">
                4.9/5
              </div>
              <div className="text-gray-600 font-medium">
                Średnia ocena
              </div>
              <div className="flex justify-center mt-2">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                ))}
              </div>
            </div>
            
            <div>
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">
                500+
              </div>
              <div className="text-gray-600 font-medium">
                Zadowolonych klientów
              </div>
            </div>
            
            <div>
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">
                98%
              </div>
              <div className="text-gray-600 font-medium">
                Poleca nas dalej
              </div>
            </div>
            
            <div>
              <div className="text-3xl md:text-4xl font-bold gradient-text mb-2">
                100%
              </div>
              <div className="text-gray-600 font-medium">
                Satysfakcji
              </div>
            </div>
          </div>
        </div>

        {/* CTA section */}
        <div className="text-center mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Dołącz do grona zadowolonych klientów
          </h3>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Skorzystaj z naszych usług i przekonaj się, dlaczego klienci wybierają SOLVICTUS. 
            Gwarantujemy profesjonalizm, dyskrecję i najwyższą jakość.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/kontakt"
              className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Skontaktuj się z nami
            </a>
            <a
              href="/opinie"
              className="border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Zobacz wszystkie opinie
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TestimonialsPreview
