import { Shield, Users, Award, Heart } from 'lucide-react'

const AboutHero = () => {
  return (
    <section className="pt-32 pb-20 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
            <Users className="w-5 h-5 text-white" />
            <span className="text-white text-sm font-medium">Poznaj nasz zespół</span>
          </div>
          
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            O nas
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-4xl mx-auto leading-relaxed">
            Jesteśmy zespołem doświadczonych specjalistów, którzy od lat pomagają w najtrudniejszych sytuacjach. 
            Nasza misja to niesienie pomocy z empatią, profesjonalizmem i pełną dyskrecją.
          </p>
        </div>

        {/* Key stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
              <Shield className="w-8 h-8 text-primary-300" />
            </div>
            <div className="text-3xl font-bold mb-2">8+</div>
            <div className="text-primary-200">Lat doświadczenia</div>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-primary-300" />
            </div>
            <div className="text-3xl font-bold mb-2">15</div>
            <div className="text-primary-200">Certyfikowanych specjalistów</div>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
              <Award className="w-8 h-8 text-primary-300" />
            </div>
            <div className="text-3xl font-bold mb-2">500+</div>
            <div className="text-primary-200">Zrealizowanych zleceń</div>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4">
              <Heart className="w-8 h-8 text-primary-300" />
            </div>
            <div className="text-3xl font-bold mb-2">98%</div>
            <div className="text-primary-200">Zadowolonych klientów</div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutHero
