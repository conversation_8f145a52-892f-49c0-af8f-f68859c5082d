import { Heart, Shield, Users, Target, Eye, Handshake } from 'lucide-react'

const MissionValues = () => {
  const values = [
    {
      icon: Heart,
      title: 'Empatia',
      description: '<PERSON><PERSON><PERSON><PERSON><PERSON>, że każda sytuacja jest wyjątkowa i wymaga indywidualnego, empatycznego podejścia. Działamy z pełnym szacunkiem dla trudnych emocji naszych klientów.'
    },
    {
      icon: Shield,
      title: 'Profesjonalizm',
      description: 'Posiadamy wszystkie wymagane certyfikaty i licencje. Nasz zespół regularnie uczestniczy w szkoleniach, aby świadczyć usługi na najwyższym poziomie.'
    },
    {
      icon: Users,
      title: 'Dyskrec<PERSON>',
      description: 'Gwarantujemy pełną poufność i dyskretne działanie. Szanujemy prywatność naszych klientów i działamy z zachowaniem najwyższych standardów etycznych.'
    },
    {
      icon: Target,
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      description: 'Stosujemy sprawdzone metody i nowoczesny sprzęt. Nasze działania są zawsze skuteczne i zgodne z najwyższymi standardami bezpieczeństwa.'
    },
    {
      icon: Eye,
      title: 'Transparentność',
      description: 'Jasno komunikujemy zakres prac, koszty i procedury. Nasi klienci zawsze wiedzą, czego mogą się spodziewać i jakie są kolejne kroki.'
    },
    {
      icon: Handshake,
      title: 'Zaufanie',
      description: 'Budujemy długotrwałe relacje oparte na wzajemnym szacunku i zaufaniu. Każdy klient może liczyć na nasze pełne wsparcie i profesjonalną pomoc.'
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Mission section */}
        <div className="text-center mb-20">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-8">
            Nasza misja
          </h2>
          <div className="max-w-4xl mx-auto">
            <p className="text-xl text-gray-600 leading-relaxed mb-8">
              Wierzymy, że każdy zasługuje na godne i profesjonalne wsparcie w najtrudniejszych chwilach życia. 
              Nasza misja to nie tylko przywracanie higieny i bezpieczeństwa, ale także niesienie pomocy 
              z empatią i szacunkiem dla ludzkiej godności.
            </p>
            <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-2xl p-8 md:p-12">
              <h3 className="text-2xl font-bold text-primary-900 mb-4">
                &quot;Pomagamy po tragedii&quot;
              </h3>
              <p className="text-primary-800 leading-relaxed">
                To nie tylko nasze motto, ale filozofia działania. Rozumiemy, że za każdym zleceniem 
                kryje się ludzka historia, często pełna bólu i trudnych emocji. Dlatego podchodzimy 
                do każdej sytuacji z najwyższą empatią, profesjonalizmem i dyskrecją.
              </p>
            </div>
          </div>
        </div>

        {/* Values section */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Nasze wartości
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Wartości, które nas kierują w codziennej pracy i budowaniu relacji z klientami.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon
              return (
                <div
                  key={index}
                  className="bg-gray-50 rounded-xl p-8 hover:bg-gray-100 transition-colors duration-300"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mb-6">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {value.title}
                  </h3>
                  
                  <p className="text-gray-600 leading-relaxed">
                    {value.description}
                  </p>
                </div>
              )
            })}
          </div>
        </div>

        {/* Vision section */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 md:p-12 text-white text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Nasza wizja
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-4xl mx-auto leading-relaxed">
            Chcemy być rozpoznawalni jako lider w branży specjalistycznego sprzątania w Polsce. 
            Dążymy do tego, aby nasze usługi były synonimem najwyższej jakości, profesjonalizmu i empatii.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">Innowacyjność</h3>
              <p className="text-primary-100 text-sm">
                Ciągle rozwijamy nasze metody i inwestujemy w najnowsze technologie
              </p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">Dostępność</h3>
              <p className="text-primary-100 text-sm">
                Rozszerzamy zasięg działania, aby pomóc większej liczbie osób w potrzebie
              </p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-3">Edukacja</h3>
              <p className="text-primary-100 text-sm">
                Podnosimy świadomość społeczną na temat specjalistycznego sprzątania
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default MissionValues
