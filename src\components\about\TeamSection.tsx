import { Mail, Phone, Award, Users } from 'lucide-react'

const TeamSection = () => {
  const teamMembers = [
    {
      name: '<PERSON> <PERSON><PERSON>',
      position: 'Dyrektor <PERSON>ny',
      specialization: 'Mikrobiologia, Dezynfekcja',
      experience: '12 lat doświadczenia',
      certifications: ['PZH', 'ISO 9001', 'Mikrobiologia medyczna'],
      description: 'Specjalista z wieloletnim doświadczeniem w mikrobiologii medycznej. Odpowiada za strategię rozwoju firmy i nadzór nad jakością usług.',
      email: 'm.kowa<PERSON><PERSON>@solvictus.pl',
      phone: '+48 123 456 701'
    },
    {
      name: 'Mgr <PERSON>',
      position: 'Kierownik Operacyjny',
      specialization: 'Zarządzanie projektami, BHP',
      experience: '8 lat doświadczenia',
      certifications: ['BHP', 'Zarządzanie projektami', 'HACCP'],
      description: 'Odpowiada za koordynację zespołów terenowych i zapewnienie najwyższych standardów bezpieczeństwa podczas realizacji zleceń.',
      email: '<EMAIL>',
      phone: '+48 123 456 702'
    },
    {
      name: 'Mgr Piotr Wiśniewski',
      position: 'Specjalista ds. Dezynfekcji',
      specialization: 'Dezynfekcja biologiczna, Ozonowanie',
      experience: '10 lat doświadczenia',
      certifications: ['PZH', 'Dezynfekcja UV', 'Ozonowanie'],
      description: 'Ekspert w dziedzinie zaawansowanych metod dezynfekcji. Specjalizuje się w eliminacji patogenów i skażeń biologicznych.',
      email: '<EMAIL>',
      phone: '+48 123 456 703'
    }
  ]

  const departments = [
    {
      name: 'Zespół Interwencyjny',
      description: 'Specjaliści dostępni 24/7 do natychmiastowej reakcji w sytuacjach kryzysowych',
      members: 6,
      icon: Users
    },
    {
      name: 'Dział Techniczny',
      description: 'Eksperci od najnowszych technologii dezynfekcji i specjalistycznego sprzętu',
      members: 4,
      icon: Award
    },
    {
      name: 'Obsługa Klienta',
      description: 'Zespół zapewniający profesjonalną obsługę i wsparcie dla klientów',
      members: 3,
      icon: Phone
    },
    {
      name: 'Kontrola Jakości',
      description: 'Specjaliści nadzorujący standardy jakości i zgodność z certyfikatami',
      members: 2,
      icon: Award
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Nasz zespół
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Poznaj ludzi, którzy stoją za sukcesem SOLVICTUS. Nasz zespół to certyfikowani specjaliści 
            z wieloletnim doświadczeniem i pasją do pomagania innym.
          </p>
        </div>

        {/* Key team members */}
        <div className="mb-20">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Kierownictwo
          </h3>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <div
                key={index}
                className="bg-gray-50 rounded-xl p-8 hover:bg-gray-100 transition-colors duration-300"
              >
                <div className="text-center mb-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-white font-bold text-xl">
                      {member.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-1">
                    {member.name}
                  </h4>
                  <p className="text-primary-600 font-medium mb-2">
                    {member.position}
                  </p>
                  <p className="text-sm text-gray-500">
                    {member.experience}
                  </p>
                </div>

                <div className="space-y-4">
                  <div>
                    <h5 className="font-semibold text-gray-900 mb-2">Specjalizacja:</h5>
                    <p className="text-sm text-gray-600">{member.specialization}</p>
                  </div>

                  <div>
                    <h5 className="font-semibold text-gray-900 mb-2">Certyfikaty:</h5>
                    <div className="flex flex-wrap gap-2">
                      {member.certifications.map((cert, certIndex) => (
                        <span
                          key={certIndex}
                          className="bg-primary-100 text-primary-700 px-2 py-1 rounded text-xs font-medium"
                        >
                          {cert}
                        </span>
                      ))}
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 leading-relaxed">
                    {member.description}
                  </p>

                  <div className="border-t border-gray-200 pt-4 space-y-2">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Mail className="w-4 h-4" />
                      <a href={`mailto:${member.email}`} className="hover:text-primary-600">
                        {member.email}
                      </a>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Phone className="w-4 h-4" />
                      <a href={`tel:${member.phone}`} className="hover:text-primary-600">
                        {member.phone}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Departments */}
        <div className="mb-20">
          <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Struktura organizacyjna
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {departments.map((dept, index) => {
              const IconComponent = dept.icon
              return (
                <div
                  key={index}
                  className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
                >
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mb-4">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 mb-2">
                    {dept.name}
                  </h4>
                  <p className="text-sm text-gray-600 mb-3 leading-relaxed">
                    {dept.description}
                  </p>
                  <div className="text-primary-600 font-medium text-sm">
                    {dept.members} specjalistów
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Join us section */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 md:p-12 text-white text-center">
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Dołącz do naszego zespołu
          </h3>
          <p className="text-primary-100 mb-8 max-w-3xl mx-auto leading-relaxed">
            Szukasz pracy w branży, która ma realny wpływ na życie ludzi? 
            Dołącz do zespołu SOLVICTUS i rozwijaj się w środowisku profesjonalistów.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Wyślij CV
            </a>
            <a
              href="/kariera"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors"
            >
              Zobacz oferty pracy
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TeamSection
