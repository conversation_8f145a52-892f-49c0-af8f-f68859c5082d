import { Building2, Shield, Clock, Award } from 'lucide-react'

const B2BHero = () => {
  return (
    <section className="pt-32 pb-20 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
            <Building2 className="w-5 h-5 text-white" />
            <span className="text-white text-sm font-medium">Rozwiązania biznesowe</span>
          </div>
          
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            Usługi dla firm
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-4xl mx-auto leading-relaxed">
            Oferujemy kompleksowe rozwiązania dla przedsiębiorstw, instytucji i organizacji. 
            Kontrakty długoterminowe, szybka reakcja i najwyższe standardy jakości.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#contact"
              className="bg-white text-primary-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors"
            >
              Otrzymaj ofertę
            </a>
            <a
              href="tel:+***********"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-900 transition-colors"
            >
              Zadzwoń: +**************
            </a>
          </div>
        </div>

        {/* Key benefits for businesses */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Clock className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold mb-2">24/7 Dostępność</h3>
            <p className="text-primary-200 text-sm">Całodobowa linia dla klientów biznesowych</p>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Shield className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold mb-2">Certyfikaty</h3>
            <p className="text-primary-200 text-sm">Wszystkie dokumenty dla ubezpieczycieli</p>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Award className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold mb-2">Doświadczenie</h3>
            <p className="text-primary-200 text-sm">Współpraca z setkami firm w Polsce</p>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Building2 className="w-6 h-6 text-white" />
            </div>
            <h3 className="font-semibold mb-2">Kontrakty</h3>
            <p className="text-primary-200 text-sm">Długoterminowe umowy serwisowe</p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default B2BHero
