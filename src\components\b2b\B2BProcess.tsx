import { Phone, FileText, Users, CheckCircle, Clock, Shield } from 'lucide-react'

const B2BProcess = () => {
  const processSteps = [
    {
      icon: Phone,
      title: 'Kontakt i konsultacja',
      description: 'Skontaktuj się z nami telefonicznie lub przez formularz. Omówimy Twoje potrzeby i wymagania.',
      duration: '15 min',
      details: [
        'Analiza potrzeb biz<PERSON>ow<PERSON>',
        'Omówienie specyfiki branży',
        'Wst<PERSON>pna wycena usług',
        'Ustalenie harmonogramu'
      ]
    },
    {
      icon: FileText,
      title: 'Przygotowanie oferty',
      description: 'Na podstawie konsultacji przygotowujemy szczegółową ofertę dostosowaną do Twojej firmy.',
      duration: '24-48h',
      details: [
        'Szczegółowa wycena usług',
        'Propozycja harmonogramu',
        '<PERSON>unk<PERSON> współpracy',
        'SLA i gwarancje'
      ]
    },
    {
      icon: Users,
      title: '<PERSON><PERSON><PERSON> i negocjacje',
      description: '<PERSON><PERSON><PERSON><PERSON> si<PERSON>, aby om<PERSON><PERSON> szczegóły oferty i dostosować ją do Twoich oczekiwań.',
      duration: '1-2h',
      details: [
        'Prezentacja oferty',
        'Negocjacje warunków',
        'Dostosowanie do budżetu',
        'Finalizacja szczegółów'
      ]
    },
    {
      icon: CheckCircle,
      title: 'Podpisanie umowy',
      description: 'Formalizujemy współpracę poprzez podpisanie umowy określającej wszystkie warunki.',
      duration: '30 min',
      details: [
        'Podpisanie kontraktu',
        'Ustalenie procedur',
        'Wyznaczenie opiekuna',
        'Rozpoczęcie współpracy'
      ]
    }
  ]

  const serviceTypes = [
    {
      title: 'Interwencje jednorazowe',
      description: 'Szybka reakcja w sytuacjach kryzysowych',
      features: [
        'Dostępność 24/7',
        'Reakcja w ciągu 2h',
        'Pełna dokumentacja',
        'Wsparcie ubezpieczeniowe'
      ],
      timeline: 'Natychmiast'
    },
    {
      title: 'Umowy serwisowe',
      description: 'Regularne usługi profilaktyczne i konserwacyjne',
      features: [
        'Harmonogram wizyt',
        'Preferencyjne ceny',
        'Priorytetowa obsługa',
        'Raporty miesięczne'
      ],
      timeline: '1-12 miesięcy'
    },
    {
      title: 'Kontrakty długoterminowe',
      description: 'Kompleksowa obsługa przez wiele lat',
      features: [
        'Najniższe ceny',
        'Dedykowany zespół',
        'Elastyczne warunki',
        'Rozwój partnerstwa'
      ],
      timeline: '1-5 lat'
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Process section */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Jak wygląda proces współpracy?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Nasz proces współpracy jest przejrzysty i dostosowany do potrzeb biznesowych. 
            Od pierwszego kontaktu do realizacji usług - wszystko jest zaplanowane.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {processSteps.map((step, index) => {
            const IconComponent = step.icon
            return (
              <div
                key={index}
                className="relative bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors duration-300"
              >
                {/* Step number */}
                <div className="absolute -top-3 -left-3 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                  {index + 1}
                </div>
                
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mb-4">
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                
                <h3 className="text-lg font-bold text-gray-900 mb-3">
                  {step.title}
                </h3>
                
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {step.description}
                </p>
                
                <div className="mb-4">
                  <div className="flex items-center space-x-2 text-primary-600 text-sm font-medium mb-3">
                    <Clock className="w-4 h-4" />
                    <span>Czas: {step.duration}</span>
                  </div>
                  
                  <ul className="space-y-1">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-center space-x-2 text-xs text-gray-600">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full flex-shrink-0"></div>
                        <span>{detail}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )
          })}
        </div>

        {/* Service types */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Rodzaje współpracy
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Oferujemy elastyczne formy współpracy dostosowane do różnych potrzeb biznesowych 
            i modeli operacyjnych naszych klientów.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {serviceTypes.map((service, index) => (
            <div
              key={index}
              className="bg-white border-2 border-gray-200 rounded-xl p-8 hover:border-primary-300 transition-colors duration-300 relative"
            >
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-4">
                  {service.description}
                </p>
                <div className="inline-flex items-center space-x-2 bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-sm font-medium">
                  <Clock className="w-4 h-4" />
                  <span>{service.timeline}</span>
                </div>
              </div>
              
              <ul className="space-y-3">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Guarantees */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 md:p-12 text-white">
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Nasze gwarancje dla klientów biznesowych
            </h3>
            <p className="text-primary-100 max-w-3xl mx-auto">
              Rozumiemy, że w biznesie liczy się pewność i przewidywalność. 
              Dlatego oferujemy konkretne gwarancje i zobowiązania.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Shield className="w-8 h-8 text-white mx-auto mb-3" />
              <h4 className="font-semibold mb-2">Gwarancja jakości</h4>
              <p className="text-primary-100 text-sm">
                100% satysfakcji lub powtórzenie usługi za darmo
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <Clock className="w-8 h-8 text-white mx-auto mb-3" />
              <h4 className="font-semibold mb-2">Gwarancja czasu</h4>
              <p className="text-primary-100 text-sm">
                Dotrzymanie ustalonych terminów lub rekompensata
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center">
              <CheckCircle className="w-8 h-8 text-white mx-auto mb-3" />
              <h4 className="font-semibold mb-2">Gwarancja bezpieczeństwa</h4>
              <p className="text-primary-100 text-sm">
                Ubezpieczenie OC do 10 mln zł i pełna odpowiedzialność
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default B2BProcess
