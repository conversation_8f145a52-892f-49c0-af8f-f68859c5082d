import {
  Building,
  Hotel,
  Hospital,
  School,
  ShoppingCart,
  Home,
  Factory,
  Plane,
  CheckCircle,
  ArrowRight
} from 'lucide-react'

const B2BServices = () => {
  const industries = [
    {
      icon: Hotel,
      title: 'Hotele i restauracje',
      description: 'Specjalistyczne sprzątanie po incydentach w obiektach hotelowych',
      services: ['Pokoje hotelowe', 'Restauracje', 'Kuchnie', 'Obszary publiczne'],
      features: ['Dyskretne działanie', 'Minimalne zakłócenia', '<PERSON><PERSON>bka reakcja']
    },
    {
      icon: Hospital,
      title: 'Placówki medyczne',
      description: 'Dezynfekcja i sprzątanie w szpitalach, klinikach i przychodniach',
      services: ['Szpitale', 'Kliniki', 'Przychodnie', 'Laboratoria'],
      features: ['Certyfikowane procedury', 'Zgodnoś<PERSON> z normami', 'Sterylne środowisko']
    },
    {
      icon: Building,
      title: 'Biura i korporacje',
      description: 'Kompleksowe usługi dla biur, centrów biznesowych i korporacji',
      services: ['Biura', 'Centra biznesowe', 'Coworkingi', 'Sale konferencyjne'],
      features: ['Poza godzinami pracy', 'Ochrona danych', 'Profesjonalizm']
    },
    {
      icon: School,
      title: 'Instytucje edukacyjne',
      description: 'Bezpieczne sprzątanie w szkołach, uczelniach i przedszkolach',
      services: ['Szkoły', 'Uczelnie', 'Przedszkola', 'Internaty'],
      features: ['Bezpieczeństwo dzieci', 'Ekologiczne środki', 'Szybka realizacja']
    },
    {
      icon: ShoppingCart,
      title: 'Centra handlowe',
      description: 'Sprzątanie w galeriach, sklepach i centrach handlowych',
      services: ['Galerie handlowe', 'Sklepy', 'Magazyny', 'Parkingi'],
      features: ['Minimalne zakłócenia', 'Elastyczne godziny', 'Duże powierzchnie']
    },
    {
      icon: Home,
      title: 'Nieruchomości',
      description: 'Usługi dla zarządców nieruchomości i wspólnot mieszkaniowych',
      services: ['Wspólnoty', 'Apartamenty', 'Domy', 'Części wspólne'],
      features: ['Współpraca z zarządcami', 'Regularne serwisy', 'Konkurencyjne ceny']
    },
    {
      icon: Factory,
      title: 'Przemysł',
      description: 'Specjalistyczne sprzątanie w zakładach przemysłowych',
      services: ['Fabryki', 'Magazyny', 'Hale produkcyjne', 'Biura zakładowe'],
      features: ['Zgodność z BHP', 'Specjalistyczny sprzęt', 'Doświadczony zespół']
    },
    {
      icon: Plane,
      title: 'Transport',
      description: 'Usługi dla branży transportowej i logistycznej',
      services: ['Lotniska', 'Dworce', 'Terminale', 'Flota pojazdów'],
      features: ['Szybka mobilizacja', 'Dostępność 24/7', 'Międzynarodowe standardy']
    }
  ]

  const contractTypes = [
    {
      title: 'Kontrakty jednorazowe',
      description: 'Interwencje w konkretnych sytuacjach kryzysowych',
      price: 'Od 2000 zł',
      features: [
        'Natychmiastowa reakcja',
        'Bezpłatna wycena',
        'Pełna dokumentacja',
        'Wsparcie ubezpieczeniowe'
      ]
    },
    {
      title: 'Umowy serwisowe',
      description: 'Regularne usługi profilaktyczne i interwencyjne',
      price: 'Od 500 zł/miesiąc',
      features: [
        'Priorytetowa obsługa',
        'Preferencyjne ceny',
        'Regularne przeglądy',
        'Dedykowany opiekun'
      ]
    },
    {
      title: 'Kontrakty długoterminowe',
      description: 'Kompleksowa obsługa przez 12-36 miesięcy',
      price: 'Indywidualna wycena',
      features: [
        'Najniższe ceny',
        'SLA gwarantowane',
        'Elastyczne warunki',
        'Rozliczenia miesięczne'
      ]
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Industries section */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Branże, które obsługujemy
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Nasze doświadczenie obejmuje szerokie spektrum branż. Każda z nich wymaga 
            specjalistycznego podejścia i dostosowanych procedur.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {industries.map((industry, index) => {
            const IconComponent = industry.icon
            return (
              <div
                key={index}
                className="bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors duration-300 card-hover"
              >
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mb-4">
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                
                <h3 className="text-lg font-bold text-gray-900 mb-3">
                  {industry.title}
                </h3>
                
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {industry.description}
                </p>
                
                <div className="mb-4">
                  <h4 className="font-semibold text-gray-900 text-sm mb-2">Obiekty:</h4>
                  <div className="flex flex-wrap gap-1">
                    {industry.services.map((service, serviceIndex) => (
                      <span
                        key={serviceIndex}
                        className="bg-primary-100 text-primary-700 px-2 py-1 rounded text-xs"
                      >
                        {service}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 text-sm mb-2">Zalety:</h4>
                  <ul className="space-y-1">
                    {industry.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center space-x-2 text-xs text-gray-600">
                        <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )
          })}
        </div>

        {/* Contract types */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Rodzaje współpracy
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Oferujemy elastyczne formy współpracy dostosowane do potrzeb i budżetu Twojej firmy.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {contractTypes.map((contract, index) => (
            <div
              key={index}
              className="bg-white border-2 border-gray-200 rounded-xl p-8 hover:border-primary-300 transition-colors duration-300 relative"
            >
              {index === 1 && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Najpopularniejsze
                  </span>
                </div>
              )}
              
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {contract.title}
                </h3>
                <p className="text-gray-600 mb-4">
                  {contract.description}
                </p>
                <div className="text-2xl font-bold gradient-text">
                  {contract.price}
                </div>
              </div>
              
              <ul className="space-y-3 mb-8">
                {contract.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center space-x-3">
                    <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <a
                href="#kontakt-b2b"
                className="w-full bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
              >
                <span>Skontaktuj się</span>
                <ArrowRight className="w-4 h-4" />
              </a>
            </div>
          ))}
        </div>

        {/* CTA section */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 md:p-12 text-white text-center">
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Potrzebujesz indywidualnej oferty?
          </h3>
          <p className="text-primary-100 mb-8 max-w-2xl mx-auto">
            Każda firma ma unikalne potrzeby. Skontaktuj się z nami, a przygotujemy 
            spersonalizowaną ofertę dostosowaną do Twojej branży i wymagań.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#contact"
              className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              Otrzymaj ofertę
            </a>
            <a
              href="tel:+48123456790"
              className="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors"
            >
              Zadzwoń: +48 123 456 790
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default B2BServices
