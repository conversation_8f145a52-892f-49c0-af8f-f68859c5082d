'use client'

import { useEffect, useRef, useMemo } from 'react'
import { MapPin, Navigation, Phone } from 'lucide-react'

declare global {
  interface Window {
    google: any
    initMap: () => void
  }
}

const ContactMap = () => {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstanceRef = useRef<any>(null)

  const companyLocation = {
    lat: 52.2297,
    lng: 21.0122,
    address: "ul. Przykładowa 123, 00-001 Warszawa"
  }

  useEffect(() => {
    const loadGoogleMaps = () => {
      if (window.google && window.google.maps) {
        initializeMap()
        return
      }

      // Create script element for Google Maps API
      const script = document.createElement('script')
      script.src = `https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap`
      script.async = true
      script.defer = true

      // Define the callback function
      window.initMap = initializeMap

      document.head.appendChild(script)
    }

    const initializeMap = () => {
      if (!mapRef.current || !window.google) return

      const map = new window.google.maps.Map(mapRef.current, {
        center: companyLocation,
        zoom: 15,
        styles: [
          {
            featureType: 'all',
            elementType: 'geometry.fill',
            stylers: [{ color: '#f8f9fa' }]
          },
          {
            featureType: 'water',
            elementType: 'geometry',
            stylers: [{ color: '#3A7DFF' }]
          },
          {
            featureType: 'road',
            elementType: 'geometry',
            stylers: [{ color: '#ffffff' }]
          }
        ]
      })

      // Custom marker
      const marker = new window.google.maps.Marker({
        position: companyLocation,
        map: map,
        title: 'SOLVICTUS Sp. z o.o.',
        icon: {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
            <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
              <circle cx="20" cy="20" r="18" fill="#0A2144" stroke="#ffffff" stroke-width="2"/>
              <circle cx="20" cy="20" r="8" fill="#ffffff"/>
              <text x="20" y="25" text-anchor="middle" fill="#0A2144" font-size="10" font-weight="bold">S</text>
            </svg>
          `),
          scaledSize: new window.google.maps.Size(40, 40)
        }
      })

      // Info window
      const infoWindow = new window.google.maps.InfoWindow({
        content: `
          <div style="padding: 10px; max-width: 250px;">
            <h3 style="margin: 0 0 8px 0; color: #0A2144; font-size: 16px; font-weight: bold;">SOLVICTUS Sp. z o.o.</h3>
            <p style="margin: 0 0 8px 0; color: #666; font-size: 14px;">${companyLocation.address}</p>
            <p style="margin: 0 0 8px 0; color: #666; font-size: 12px;">Profesjonalne sprzątanie po tragedii</p>
            <div style="display: flex; gap: 8px; margin-top: 10px;">
              <a href="tel:+48123456789" style="background: #0A2144; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;">Zadzwoń</a>
              <a href="https://www.google.com/maps/dir/?api=1&destination=${companyLocation.lat},${companyLocation.lng}" target="_blank" style="background: #3A7DFF; color: white; padding: 6px 12px; text-decoration: none; border-radius: 4px; font-size: 12px;">Trasa</a>
            </div>
          </div>
        `
      })

      marker.addListener('click', () => {
        infoWindow.open(map, marker)
      })

      mapInstanceRef.current = map
    }

    loadGoogleMaps()

    return () => {
      // Cleanup
      if ((window as any).initMap) {
        delete (window as any).initMap
      }
    }
  }, [companyLocation])

  const handleGetDirections = () => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${companyLocation.lat},${companyLocation.lng}`
    window.open(url, '_blank')
  }

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Nasza lokalizacja
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Nasze biuro znajduje się w centrum Warszawy. Działamy jednak w całej Polsce, 
            zapewniając szybką reakcję w sytuacjach kryzysowych.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Google Maps */}
          <div className="lg:col-span-2">
            <div className="bg-gray-200 rounded-xl overflow-hidden shadow-lg h-96 lg:h-[500px] relative">
              <div
                ref={mapRef}
                className="w-full h-full"
                style={{ minHeight: '400px' }}
              />

              {/* Loading overlay */}
              <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-primary-100 to-primary-200" id="map-loading">
                <div className="text-center">
                  <MapPin className="w-16 h-16 text-primary-600 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-primary-900 mb-2">
                    Ładowanie mapy...
                  </h3>
                  <p className="text-primary-700 mb-4">
                    ul. Przykładowa 123<br />
                    00-001 Warszawa
                  </p>
                  <button
                    onClick={handleGetDirections}
                    className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2 mx-auto"
                  >
                    <Navigation className="w-5 h-5" />
                    <span>Wyznacz trasę</span>
                  </button>
                </div>
              </div>

              {/* Map controls */}
              <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-3">
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                  <span>Biuro główne</span>
                </div>
              </div>

              {/* Quick actions */}
              <div className="absolute bottom-4 left-4 flex space-x-2">
                <button
                  onClick={handleGetDirections}
                  className="bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg shadow-lg text-sm font-medium transition-colors flex items-center space-x-2"
                >
                  <Navigation className="w-4 h-4" />
                  <span>Trasa</span>
                </button>
                <a
                  href="tel:+48123456789"
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg shadow-lg text-sm font-medium transition-colors flex items-center space-x-2"
                >
                  <Phone className="w-4 h-4" />
                  <span>Zadzwoń</span>
                </a>
              </div>
            </div>

            <div className="mt-4 text-center">
              <p className="text-sm text-gray-500">
                Kliknij marker na mapie, aby zobaczyć szczegóły i opcje nawigacji
              </p>
            </div>
          </div>

          {/* Location info */}
          <div className="space-y-6">
            <div className="bg-gray-50 rounded-xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <MapPin className="w-6 h-6 text-primary-600" />
                <h3 className="text-lg font-bold text-gray-900">Adres biura</h3>
              </div>
              <div className="space-y-2 text-gray-600">
                <p>SOLVICTUS Sp. z o.o.</p>
                <p>ul. Przykładowa 123</p>
                <p>00-001 Warszawa</p>
                <p>Polska</p>
              </div>
              <button
                onClick={handleGetDirections}
                className="mt-4 w-full bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
              >
                <Navigation className="w-4 h-4" />
                <span>Wyznacz trasę</span>
              </button>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-xl p-6">
              <div className="flex items-center space-x-3 mb-4">
                <Phone className="w-6 h-6 text-red-600" />
                <h3 className="text-lg font-bold text-red-900">Sytuacja kryzysowa?</h3>
              </div>
              <p className="text-red-800 mb-4">
                Nie przyjeżdżaj do biura. Zadzwoń natychmiast, a nasz zespół przyjedzie do Ciebie.
              </p>
              <a
                href="tel:+48123456789"
                className="block w-full bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg font-bold text-center transition-colors"
              >
                +48 123 456 789
              </a>
            </div>

            <div className="bg-white border border-gray-200 rounded-xl p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Dojazd komunikacją</h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div>
                  <span className="font-medium">Metro:</span>
                  <p>Stacja Centrum (linia M1) - 5 min pieszo</p>
                </div>
                <div>
                  <span className="font-medium">Autobus:</span>
                  <p>Linie: 175, 180, 503 - przystanek Plac Konstytucji</p>
                </div>
                <div>
                  <span className="font-medium">Tramwaj:</span>
                  <p>Linie: 4, 15, 18, 35 - przystanek Nowy Świat</p>
                </div>
                <div>
                  <span className="font-medium">Parking:</span>
                  <p>Płatne miejsca parkingowe w strefie płatnego parkowania</p>
                </div>
              </div>
            </div>

            <div className="bg-primary-50 border border-primary-200 rounded-xl p-6">
              <h3 className="text-lg font-bold text-primary-900 mb-4">Zasięg działania</h3>
              <p className="text-primary-800 text-sm mb-3">
                Choć nasze biuro znajduje się w Warszawie, działamy w całej Polsce:
              </p>
              <ul className="space-y-1 text-sm text-primary-700">
                <li>• Warszawa i okolice - do 2h</li>
                <li>• Główne miasta - do 4h</li>
                <li>• Cała Polska - do 8h</li>
                <li>• Sytuacje kryzysowe - priorytet</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ContactMap
