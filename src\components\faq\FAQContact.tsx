import { Phone, Mail, MessageCircle, Clock } from 'lucide-react'

const FAQContact = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Ni<PERSON>ł<PERSON>ś odpowiedzi?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Nasz zespół jest gotowy odpowiedzieć na wszystkie Twoje pytania. 
            Skontaktuj się z nami w dogodny dla Ciebie sposób.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          <div className="bg-white rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Phone className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Linia kryzysowa
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Dostępna 24/7 dla pilnych sytuacji
            </p>
            <a
              href="tel:+48123456789"
              className="text-red-600 hover:text-red-700 font-semibold"
            >
              +48 123 456 789
            </a>
          </div>

          <div className="bg-white rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Mail className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Email
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Odpowiedź w ciągu 24 godzin
            </p>
            <a
              href="mailto:<EMAIL>"
              className="text-primary-600 hover:text-primary-700 font-semibold"
            >
              <EMAIL>
            </a>
          </div>

          <div className="bg-white rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <MessageCircle className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Formularz
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Szczegółowe zapytanie online
            </p>
            <a
              href="/kontakt"
              className="text-green-600 hover:text-green-700 font-semibold"
            >
              Wypełnij formularz
            </a>
          </div>

          <div className="bg-white rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Clock className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Godziny pracy
            </h3>
            <p className="text-gray-600 text-sm mb-4">
              Biuro: Pn-Pt 8:00-18:00
            </p>
            <span className="text-blue-600 font-semibold">
              Interwencje: 24/7
            </span>
          </div>
        </div>

        {/* Quick help section */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 md:p-12 text-white text-center">
          <h3 className="text-2xl md:text-3xl font-bold mb-4">
            Potrzebujesz natychmiastowej pomocy?
          </h3>
          <p className="text-primary-100 mb-8 max-w-2xl mx-auto">
            W sytuacjach kryzysowych każda minuta się liczy. Nasz zespół jest gotowy 
            do natychmiastowej interwencji w całej Polsce.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="tel:+48123456789"
              className="bg-white text-primary-600 px-8 py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2"
            >
              <Phone className="w-5 h-5" />
              <span>Zadzwoń natychmiast</span>
            </a>
            <a
              href="/kontakt"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-600 transition-colors"
            >
              Wyślij zapytanie
            </a>
          </div>
        </div>

        {/* Additional resources */}
        <div className="mt-12 text-center">
          <h3 className="text-xl font-bold text-gray-900 mb-6">
            Przydatne zasoby
          </h3>
          <div className="flex flex-wrap justify-center gap-4">
            <a
              href="/blog"
              className="bg-white border border-gray-200 hover:border-primary-300 px-6 py-3 rounded-lg text-gray-700 hover:text-primary-600 transition-colors"
            >
              Blog i poradniki
            </a>
            <a
              href="/certyfikaty"
              className="bg-white border border-gray-200 hover:border-primary-300 px-6 py-3 rounded-lg text-gray-700 hover:text-primary-600 transition-colors"
            >
              Nasze certyfikaty
            </a>
            <a
              href="/procedury"
              className="bg-white border border-gray-200 hover:border-primary-300 px-6 py-3 rounded-lg text-gray-700 hover:text-primary-600 transition-colors"
            >
              Procedury bezpieczeństwa
            </a>
            <a
              href="/regulamin"
              className="bg-white border border-gray-200 hover:border-primary-300 px-6 py-3 rounded-lg text-gray-700 hover:text-primary-600 transition-colors"
            >
              Regulamin usług
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FAQContact
