import { HelpCircle, MessageCircle, Phone } from 'lucide-react'

const FAQHero = () => {
  return (
    <section className="pt-32 pb-20 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
          <HelpCircle className="w-5 h-5 text-white" />
          <span className="text-white text-sm font-medium">Centrum pomocy</span>
        </div>
        
        <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
          C<PERSON><PERSON>sto zadawane pytania
        </h1>
        
        <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-4xl mx-auto leading-relaxed">
          Znajdź odpowiedzi na najczęściej zadawane pytania dotyczące naszych usług, 
          procedur i procesów. Jeś<PERSON> nie znajdziesz odpowiedzi, skontaktuj się z nami.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="tel:+48123456789"
            className="bg-white text-primary-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2"
          >
            <Phone className="w-5 h-5" />
            <span>Zadzwoń teraz</span>
          </a>
          <a
            href="/kontakt"
            className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-900 transition-colors flex items-center justify-center space-x-2"
          >
            <MessageCircle className="w-5 h-5" />
            <span>Napisz do nas</span>
          </a>
        </div>
      </div>
    </section>
  )
}

export default FAQHero
