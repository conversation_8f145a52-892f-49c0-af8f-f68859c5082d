import { Camera, Eye, Shield } from 'lucide-react'

const GalleryHero = () => {
  return (
    <section className="pt-32 pb-20 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
            <Camera className="w-5 h-5 text-white" />
            <span className="text-white text-sm font-medium">Nasze realizacje</span>
          </div>
          
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            Galeria realizacji
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-4xl mx-auto leading-relaxed">
            Zobacz efekty naszej pracy. Zdjęcia przed i po sprzątaniu pokazują 
            profesjonalizm i skuteczność naszych działań.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
              <Eye className="w-5 h-5" />
              <span>Dyskretne dokumentowanie</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2">
              <Shield className="w-5 h-5" />
              <span>Zgodność z RODO</span>
            </div>
          </div>
        </div>

        {/* Important notice */}
        <div className="bg-yellow-500/20 border border-yellow-400/30 rounded-xl p-6 max-w-4xl mx-auto">
          <div className="flex items-start space-x-3">
            <Shield className="w-6 h-6 text-yellow-300 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-semibold text-yellow-200 mb-2">
                Ważna informacja o prywatności
              </h3>
              <p className="text-yellow-100 text-sm leading-relaxed">
                Wszystkie zdjęcia zostały wykonane za zgodą klientów i są prezentowane 
                z poszanowaniem prywatności. Twarze osób i dane osobowe zostały zanonimizowane. 
                Dokumentujemy nasze prace wyłącznie w celach edukacyjnych i promocyjnych.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default GalleryHero
