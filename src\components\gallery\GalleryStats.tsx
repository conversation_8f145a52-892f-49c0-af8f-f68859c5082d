import { Camera, CheckCircle, Clock, Star } from 'lucide-react'

const GalleryStats = () => {
  const stats = [
    {
      icon: Camera,
      number: '500+',
      label: 'Udokumentowanych realizacji',
      description: 'Każda praca jest profesjonalnie udokumentowana'
    },
    {
      icon: CheckCircle,
      number: '100%',
      label: 'Zadowolonych klientów',
      description: 'Wszystkie projekty zakończone sukcesem'
    },
    {
      icon: Clock,
      number: '8',
      label: 'Lat doświadczenia',
      description: 'Wieloletnia praktyka w branży'
    },
    {
      icon: Star,
      number: '4.9/5',
      label: 'Średnia ocena',
      description: 'Najwyższe oceny od naszych klientów'
    }
  ]

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Nasze osiągnięcia w liczbach
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Każda realizacja to dowód naszego profesjonalizmu i zaangażowania 
            w przywracanie bezpieczeństwa i higieny.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <div
                key={index}
                className="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-300"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <IconComponent className="w-8 h-8 text-white" />
                </div>
                
                <div className="text-3xl font-bold gradient-text mb-2">
                  {stat.number}
                </div>
                
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {stat.label}
                </h3>
                
                <p className="text-gray-600 text-sm">
                  {stat.description}
                </p>
              </div>
            )
          })}
        </div>

        {/* Additional info */}
        <div className="mt-12 bg-gradient-to-r from-primary-50 to-primary-100 rounded-2xl p-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-primary-900 mb-4">
              Dlaczego dokumentujemy nasze prace?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="bg-white rounded-lg p-4">
                <h4 className="font-semibold text-primary-900 mb-2">Transparentność</h4>
                <p className="text-primary-800 text-sm">
                  Pokazujemy rzeczywiste efekty naszej pracy i proces realizacji
                </p>
              </div>
              <div className="bg-white rounded-lg p-4">
                <h4 className="font-semibold text-primary-900 mb-2">Edukacja</h4>
                <p className="text-primary-800 text-sm">
                  Pomagamy zrozumieć złożoność procesu specjalistycznego sprzątania
                </p>
              </div>
              <div className="bg-white rounded-lg p-4">
                <h4 className="font-semibold text-primary-900 mb-2">Zaufanie</h4>
                <p className="text-primary-800 text-sm">
                  Budujemy zaufanie poprzez pokazanie prawdziwych rezultatów
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default GalleryStats
