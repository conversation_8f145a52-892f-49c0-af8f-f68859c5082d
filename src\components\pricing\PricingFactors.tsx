import { AlertTrian<PERSON>, Clock, MapPin, Gauge } from 'lucide-react'

const PricingFactors = () => {
  const factors = [
    {
      icon: Gauge,
      title: '<PERSON>ie<PERSON> trudności',
      description: 'Poziom skomplikowania i intensywności prac',
      examples: [
        'Niska: Standardowe sprzątanie, łatwy dostęp',
        'Średnia: Umiarkowane skażenie, normalne warunki',
        'Wysoka: Intensywne skażenie, trudne warunki',
        'Ekstremalna: Skrajne przypadki, specjalistyczny sprzęt'
      ]
    },
    {
      icon: Clock,
      title: 'Pilnoś<PERSON> zlecenia',
      description: 'Czas reakcji i dostępność zespołu',
      examples: [
        'Planowana: Elastyczny termin (-10%)',
        'Normalna: Standardowy czas realizacji',
        'Pilna: Do 24h (+30%)',
        'Natychmiastowa: Do 2h (+50%)'
      ]
    },
    {
      icon: MapPin,
      title: 'Lokalizacja',
      description: 'Odle<PERSON><PERSON><PERSON><PERSON><PERSON> od naszej bazy i dostępność',
      examples: [
        'Warszawa: Standardowa cena',
        'Duże miasta: +10%',
        'Średnie miasta: +20%',
        'Małe miejscowości: +30%',
        'Obszary wiejskie: +50%'
      ]
    },
    {
      icon: AlertTriangle,
      title: 'Specyfika przypadku',
      description: 'Dodatkowe czynniki wpływające na cenę',
      examples: [
        'Powierzchnia do sprzątania',
        'Rodzaj skażenia biologicznego',
        'Dostępność mediów (prąd, woda)',
        'Wymagania specjalne klienta',
        'Konieczność pracy nocnej/weekendowej'
      ]
    }
  ]

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Czynniki wpływające na cenę
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Każda sytuacja jest unikalna. Nasza wycena uwzględnia wszystkie istotne 
            czynniki, aby zapewnić uczciwe i przejrzyste ceny.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {factors.map((factor, index) => {
            const IconComponent = factor.icon
            return (
              <div
                key={index}
                className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
              >
                <div className="flex items-center space-x-4 mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">
                      {factor.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {factor.description}
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  {factor.examples.map((example, exampleIndex) => (
                    <div
                      key={exampleIndex}
                      className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-700 text-sm">{example}</span>
                    </div>
                  ))}
                </div>
              </div>
            )
          })}
        </div>

        {/* Important note */}
        <div className="mt-16 bg-primary-50 border border-primary-200 rounded-2xl p-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-primary-900 mb-4">
              Bezpłatna wycena na miejscu
            </h3>
            <p className="text-primary-800 mb-6 max-w-3xl mx-auto">
              Każda sytuacja jest inna. Dlatego oferujemy bezpłatne oględziny i szczegółową 
              wycenę na miejscu. Nie ma ukrytych kosztów - wszystko jest jasno określone przed 
              rozpoczęciem prac.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/kontakt"
                className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                Umów bezpłatną wycenę
              </a>
              <a
                href="tel:+48123456789"
                className="border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                Zadzwoń: +48 123 456 789
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default PricingFactors
