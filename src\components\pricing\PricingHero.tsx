'use client'

import { Calculator, DollarSign, CheckCircle } from 'lucide-react'

const PricingHero = () => {
  return (
    <section className="pt-32 pb-20 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
            <DollarSign className="w-5 h-5 text-white" />
            <span className="text-white text-sm font-medium">Przejrzyste ceny</span>
          </div>
          
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            Cennik usług
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-4xl mx-auto leading-relaxed">
            Oferujemy uczciwe i przejrzyste ceny za nasze usługi. 
            Każda wycena jest indywidualna i dostosowana do specyfiki sytuacji.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#calculator"
              className="bg-white text-primary-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2"
            >
              <Calculator className="w-5 h-5" />
              <span>Kalkulator kosztów</span>
            </a>
            <a
              href="/kontakt"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-900 transition-colors"
            >
              Bezpłatna wycena
            </a>
          </div>
        </div>

        {/* Key benefits */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <CheckCircle className="w-8 h-8 text-primary-300 mx-auto mb-4" />
            <h3 className="font-semibold mb-2">Bez ukrytych kosztów</h3>
            <p className="text-primary-200 text-sm">
              Wszystkie koszty są jasno określone przed rozpoczęciem prac
            </p>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <Calculator className="w-8 h-8 text-primary-300 mx-auto mb-4" />
            <h3 className="font-semibold mb-2">Wycena online</h3>
            <p className="text-primary-200 text-sm">
              Skorzystaj z kalkulatora lub otrzymaj bezpłatną wycenę
            </p>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <DollarSign className="w-8 h-8 text-primary-300 mx-auto mb-4" />
            <h3 className="font-semibold mb-2">Elastyczne płatności</h3>
            <p className="text-primary-200 text-sm">
              Różne formy płatności, możliwość rozłożenia na raty
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default PricingHero
