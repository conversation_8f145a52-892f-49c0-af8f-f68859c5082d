'use client'

import { CheckCircle, X, Star } from 'lucide-react'

const PricingTables = () => {
  const services = [
    {
      name: '<PERSON><PERSON>rzątanie po zgonach',
      description: 'Kompleksowe sprzątanie i dezynfekcja po śmierci',
      basePrice: 'od 2 000 zł',
      priceRange: '2 000 - 8 000 zł',
      factors: [
        'Powierzchnia pomieszczenia',
        'Stopień skażenia',
        'Czas od zdarzenia',
        'Dost<PERSON><PERSON><PERSON>ść lokalu'
      ],
      included: [
        'Dezynfekcja biologiczna',
        'Usuwanie zapachów',
        'Ozonowanie',
        'Utylizacja odpadów',
        'Certyfikat wykonania'
      ],
      notIncluded: [
        '<PERSON>wa<PERSON><PERSON> powierzchni',
        'Malowanie ścian',
        '<PERSON><PERSON>iana podłóg'
      ],
      popular: true
    },
    {
      name: 'Sprzątanie po pożarach',
      description: 'Usuwanie skutków pożaru i zadymienia',
      basePrice: 'od 3 000 zł',
      priceRange: '3 000 - 15 000 zł',
      factors: [
        '<PERSON><PERSON><PERSON><PERSON> powierzchni',
        '<PERSON>ień uszkodzeń',
        '<PERSON><PERSON>j materiałów',
        'Dostęp do lokalu'
      ],
      included: [
        'Usuwanie sadzy',
        'Neutralizacja zapachów',
        'Czyszczenie instalacji',
        'Dezynfekcja',
        'Dokumentacja szkód'
      ],
      notIncluded: [
        'Remont konstrukcji',
        'Wymiana instalacji',
        'Odbudowa elementów'
      ],
      popular: false
    },
    {
      name: 'Usuwanie skutków powodzi',
      description: 'Osuszanie i dezynfekcja po zalaniach',
      basePrice: 'od 1 500 zł',
      priceRange: '1 500 - 10 000 zł',
      factors: [
        'Powierzchnia zalania',
        'Wysokość wody',
        'Czas ekspozycji',
        'Rodzaj materiałów'
      ],
      included: [
        'Osuszanie pomieszczeń',
        'Dezynfekcja przeciwgrzybicza',
        'Monitoring wilgotności',
        'Wentylacja',
        'Raport końcowy'
      ],
      notIncluded: [
        'Remont wykończeń',
        'Wymiana izolacji',
        'Odbudowa elementów'
      ],
      popular: false
    },
    {
      name: 'Dezynfekcja biologiczna',
      description: 'Eliminacja patogenów i skażeń',
      basePrice: 'od 800 zł',
      priceRange: '800 - 5 000 zł',
      factors: [
        'Powierzchnia obiektu',
        'Rodzaj patogenów',
        'Stopień skażenia',
        'Wymagane certyfikaty'
      ],
      included: [
        'Dezynfekcja powierzchni',
        'Nebulizacja',
        'Dezynfekcja powietrza',
        'Kontrola skuteczności',
        'Certyfikat PZH'
      ],
      notIncluded: [
        'Testy laboratoryjne',
        'Monitoring długoterminowy',
        'Dodatkowe wizyty'
      ],
      popular: false
    },
    {
      name: 'Ozonowanie pomieszczeń',
      description: 'Neutralizacja zapachów metodą ozonowania',
      basePrice: 'od 500 zł',
      priceRange: '500 - 2 000 zł',
      factors: [
        'Kubatura pomieszczenia',
        'Intensywność zapachu',
        'Czas ekspozycji',
        'Liczba sesji'
      ],
      included: [
        'Generacja ozonu',
        'Neutralizacja zapachów',
        'Dezynfekcja powietrza',
        'Kontrola jakości',
        'Instrukcja bezpieczeństwa'
      ],
      notIncluded: [
        'Usuwanie źródła zapachu',
        'Czyszczenie powierzchni',
        'Dodatkowe wizyty'
      ],
      popular: false
    },
    {
      name: 'Usługi dla firm',
      description: 'Kontrakty i umowy serwisowe',
      basePrice: 'od 1 000 zł/mies.',
      priceRange: 'Wycena indywidualna',
      factors: [
        'Rodzaj działalności',
        'Powierzchnia obiektu',
        'Częstotliwość wizyt',
        'Zakres usług'
      ],
      included: [
        'Priorytetowa obsługa',
        'Preferencyjne ceny',
        'Dedykowany opiekun',
        'Raporty miesięczne',
        'SLA gwarantowane'
      ],
      notIncluded: [
        'Materiały eksploatacyjne',
        'Dodatkowe interwencje',
        'Usługi poza umową'
      ],
      popular: false
    }
  ]

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Cennik usług
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Nasze ceny są konkurencyjne i dostosowane do złożoności każdej sytuacji. 
            Wszystkie wyceny są bezpłatne i bez zobowiązań.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className={`bg-white rounded-xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl ${
                service.popular 
                  ? 'border-primary-500 relative' 
                  : 'border-gray-200 hover:border-primary-300'
              }`}
            >
              {service.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <div className="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <Star className="w-4 h-4" />
                    <span>Najpopularniejsze</span>
                  </div>
                </div>
              )}

              <div className="p-8">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  {service.name}
                </h3>
                <p className="text-gray-600 mb-4">
                  {service.description}
                </p>

                <div className="mb-6">
                  <div className="text-3xl font-bold gradient-text mb-1">
                    {service.basePrice}
                  </div>
                  <div className="text-sm text-gray-500">
                    Zakres: {service.priceRange}
                  </div>
                </div>

                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">Czynniki wpływające na cenę:</h4>
                  <ul className="space-y-2">
                    {service.factors.map((factor, factorIndex) => (
                      <li key={factorIndex} className="flex items-center space-x-2 text-sm text-gray-600">
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full"></div>
                        <span>{factor}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">W cenie:</h4>
                  <ul className="space-y-2">
                    {service.included.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center space-x-2 text-sm text-gray-700">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mb-8">
                  <h4 className="font-semibold text-gray-900 mb-3">Dodatkowo płatne:</h4>
                  <ul className="space-y-2">
                    {service.notIncluded.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center space-x-2 text-sm text-gray-500">
                        <X className="w-4 h-4 text-gray-400 flex-shrink-0" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <button className={`w-full py-3 px-6 rounded-lg font-semibold transition-colors ${
                  service.popular
                    ? 'bg-primary-600 hover:bg-primary-700 text-white'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                }`}>
                  Otrzymaj wycenę
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Additional info */}
        <div className="mt-16 bg-gray-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ważne informacje o cenach
            </h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Wycena bezpłatna</h4>
              <p className="text-gray-600 text-sm">Każda wycena jest darmowa i bez zobowiązań</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Ceny finalne</h4>
              <p className="text-gray-600 text-sm">Brak ukrytych kosztów i dodatkowych opłat</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Elastyczne płatności</h4>
              <p className="text-gray-600 text-sm">Gotówka, przelew, karta, możliwość rat</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <CheckCircle className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-gray-900 mb-2">Ubezpieczenia</h4>
              <p className="text-gray-600 text-sm">Współpracujemy z firmami ubezpieczeniowymi</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default PricingTables
