'use client'

import { Star, Calendar, MapPin, CheckCircle } from 'lucide-react'

const ReviewsGrid = () => {
  const reviews = [
    {
      id: 1,
      name: '<PERSON>',
      location: 'Warszawa',
      service: 'Sprzątanie po zgonie',
      rating: 5,
      date: '2024-01-15',
      verified: true,
      title: 'Profesjonalizm w najtrudniejszej chwili',
      content: 'W najtrudniejszej chwili mojego życia zespół SOLVICTUS okazał się niezwykle profesjonalny i empatyczny. Wszystko zostało wykonane dyskretnie i z najwyższą starannością. Jestem bardzo wdzięczna za ich pomoc i wsparcie.',
      helpful: 23,
      avatar: 'AK'
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      location: 'Kraków',
      service: 'Sprzątanie po pożarze',
      rating: 5,
      date: '2024-01-10',
      verified: true,
      title: 'Odzyskaliśmy nadzieję',
      content: '<PERSON> pożarze w naszym domu myśleliśmy, że wszystko stracone. SOLVICTUS nie tylko profesjonalnie usunął skutki pożaru, ale także pomógł nam odzyskać nadzieję. Polecam z całego serca.',
      helpful: 18,
      avatar: 'MS'
    },
    {
      id: 3,
      name: 'Firma ABC Sp. z o.o.',
      location: 'Gdańsk',
      service: 'Usługi dla firm',
      rating: 5,
      date: '2024-01-05',
      verified: true,
      title: 'Niezawodny partner biznesowy',
      content: 'Współpracujemy z SOLVICTUS od 3 lat. Ich profesjonalizm, punktualność i jakość usług są na najwyższym poziomie. To partner, na którego zawsze możemy liczyć.',
      helpful: 31,
      avatar: 'ABC'
    },
    {
      id: 4,
      name: 'Katarzyna W.',
      location: 'Wrocław',
      service: 'Dezynfekcja biologiczna',
      rating: 5,
      date: '2023-12-28',
      verified: true,
      title: 'Szybka i skuteczna pomoc',
      content: 'Po wykryciu skażenia w naszym biurze SOLVICTUS zareagował natychmiast. Dezynfekcja została przeprowadzona profesjonalnie, otrzymaliśmy wszystkie certyfikaty. Bardzo polecam!',
      helpful: 15,
      avatar: 'KW'
    },
    {
      id: 5,
      name: 'Piotr L.',
      location: 'Poznań',
      service: 'Usuwanie skutków powodzi',
      rating: 4,
      date: '2023-12-20',
      verified: true,
      title: 'Skuteczne osuszanie',
      content: 'Po zalaniu piwnicy zespół SOLVICTUS szybko i skutecznie usunął wodę oraz przeprowadził osuszanie. Jedynym minusem był nieco wyższy koszt niż oczekiwałem, ale jakość usług była bardzo dobra.',
      helpful: 12,
      avatar: 'PL'
    },
    {
      id: 6,
      name: 'Maria D.',
      location: 'Łódź',
      service: 'Ozonowanie pomieszczeń',
      rating: 5,
      date: '2023-12-15',
      verified: true,
      title: 'Zapach całkowicie usunięty',
      content: 'Mieliśmy problem z nieprzyjemnym zapachem w restauracji. Ozonowanie przeprowadzone przez SOLVICTUS całkowicie rozwiązało problem. Klienci już nie narzekają!',
      helpful: 9,
      avatar: 'MD'
    },
    {
      id: 7,
      name: 'Tomasz K.',
      location: 'Szczecin',
      service: 'Sprzątanie po zgonie',
      rating: 5,
      date: '2023-12-10',
      verified: true,
      title: 'Empatia i profesjonalizm',
      content: 'Bardzo trudna sytuacja, ale zespół SOLVICTUS podszedł do niej z pełną empatią i profesjonalizmem. Wszystko zostało załatwione dyskretnie i sprawnie. Dziękuję za wsparcie.',
      helpful: 27,
      avatar: 'TK'
    },
    {
      id: 8,
      name: 'Hotel Marriott',
      location: 'Warszawa',
      service: 'Usługi dla firm',
      rating: 5,
      date: '2023-12-05',
      verified: true,
      title: 'Strategiczny partner',
      content: 'SOLVICTUS to nasz strategiczny partner od kilku lat. Zawsze możemy liczyć na szybką reakcję i najwyższą jakość usług. Ich dyskrecja jest bezcenna w branży hotelarskiej.',
      helpful: 22,
      avatar: 'HM'
    },
    {
      id: 9,
      name: 'Agnieszka R.',
      location: 'Katowice',
      service: 'Dezynfekcja biologiczna',
      rating: 5,
      date: '2023-11-28',
      verified: true,
      title: 'Bezpieczeństwo przywrócone',
      content: 'Po incydencie w naszej klinice potrzebowaliśmy profesjonalnej dezynfekcji. SOLVICTUS wykonał pracę zgodnie z najwyższymi standardami medycznymi. Czujemy się bezpiecznie.',
      helpful: 14,
      avatar: 'AR'
    }
  ]

  const getServiceColor = (service: string) => {
    const colors: { [key: string]: string } = {
      'Sprzątanie po zgonie': 'bg-red-50 text-red-700',
      'Sprzątanie po pożarze': 'bg-orange-50 text-orange-700',
      'Usuwanie skutków powodzi': 'bg-blue-50 text-blue-700',
      'Dezynfekcja biologiczna': 'bg-green-50 text-green-700',
      'Ozonowanie pomieszczeń': 'bg-cyan-50 text-cyan-700',
      'Usługi dla firm': 'bg-purple-50 text-purple-700'
    }
    return colors[service] || 'bg-gray-50 text-gray-700'
  }

  return (
    <section id="reviews" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Opinie naszych klientów
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Każda opinia jest dla nas ważna. Czytaj prawdziwe recenzje od osób, 
            które skorzystały z naszych usług.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {reviews.map((review) => (
            <div
              key={review.id}
              className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300 card-hover"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">{review.avatar}</span>
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-gray-900">{review.name}</h3>
                      {review.verified && (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      )}
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <MapPin className="w-3 h-3" />
                      <span>{review.location}</span>
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="flex items-center space-x-1 mb-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-4 h-4 ${
                          i < review.rating
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <div className="text-xs text-gray-500">{review.rating}/5</div>
                </div>
              </div>

              {/* Service and date */}
              <div className="flex items-center justify-between mb-4">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getServiceColor(review.service)}`}>
                  {review.service}
                </span>
                <div className="flex items-center space-x-1 text-xs text-gray-500">
                  <Calendar className="w-3 h-3" />
                  <span>{new Date(review.date).toLocaleDateString('pl-PL')}</span>
                </div>
              </div>

              {/* Review content */}
              <div className="mb-4">
                <h4 className="font-semibold text-gray-900 mb-2">
                  {review.title}
                </h4>
                <p className="text-gray-700 text-sm leading-relaxed">
                  {review.content}
                </p>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <button className="flex items-center space-x-2 text-sm text-gray-500 hover:text-gray-700 transition-colors">
                  <span>👍 Pomocne ({review.helpful})</span>
                </button>
                <div className="text-xs text-gray-400">
                  {review.verified ? 'Zweryfikowana' : 'Niezweryfikowana'}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load more */}
        <div className="text-center mt-12">
          <button className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-3 rounded-lg font-medium transition-colors">
            Załaduj więcej opinii
          </button>
        </div>
      </div>
    </section>
  )
}

export default ReviewsGrid
