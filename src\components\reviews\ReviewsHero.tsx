'use client'

import { Star, MessageSquare, ThumbsUp } from 'lucide-react'

const ReviewsHero = () => {
  return (
    <section className="pt-32 pb-20 bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-6">
            <MessageSquare className="w-5 h-5 text-white" />
            <span className="text-white text-sm font-medium">Opini<PERSON> klientów</span>
          </div>
          
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            Co mówią o nas klienci
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-4xl mx-auto leading-relaxed">
            Przeczytaj prawdziwe opinie naszych klientów. Każda recenzja to dowód 
            na profesjonalizm i empatię naszego zespołu.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="#add-review"
              className="bg-white text-primary-900 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2"
            >
              <Star className="w-5 h-5" />
              <span>Dodaj opinię</span>
            </a>
            <a
              href="#reviews"
              className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-900 transition-colors"
            >
              Zobacz wszystkie
            </a>
          </div>
        </div>

        {/* Quick stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <div className="flex items-center justify-center space-x-1 mb-2">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
              ))}
            </div>
            <div className="text-3xl font-bold mb-2">4.9/5</div>
            <div className="text-primary-200">Średnia ocena</div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <ThumbsUp className="w-8 h-8 text-primary-300 mx-auto mb-4" />
            <div className="text-3xl font-bold mb-2">98%</div>
            <div className="text-primary-200">Poleca nas dalej</div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
            <MessageSquare className="w-8 h-8 text-primary-300 mx-auto mb-4" />
            <div className="text-3xl font-bold mb-2">127</div>
            <div className="text-primary-200">Opinii klientów</div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ReviewsHero
