'use client'

import { Star, TrendingUp, Users, Award } from 'lucide-react'

const ReviewsStats = () => {
  const stats = [
    {
      icon: Star,
      value: '4.9',
      label: 'Średnia ocena',
      description: 'Na podstawie 127 opinii',
      color: 'text-yellow-600'
    },
    {
      icon: TrendingUp,
      value: '98%',
      label: '<PERSON><PERSON> nas dalej',
      description: 'Zadowoleni klienci',
      color: 'text-green-600'
    },
    {
      icon: Users,
      value: '500+',
      label: 'Obsłużonych klientów',
      description: 'W ciągu ostatnich 3 lat',
      color: 'text-blue-600'
    },
    {
      icon: Award,
      value: '100%',
      label: 'Zrealizowanych zleceń',
      description: '<PERSON>z reklamacji',
      color: 'text-purple-600'
    }
  ]

  const ratingDistribution = [
    { stars: 5, count: 98, percentage: 77 },
    { stars: 4, count: 23, percentage: 18 },
    { stars: 3, count: 4, percentage: 3 },
    { stars: 2, count: 2, percentage: 2 },
    { stars: 1, count: 0, percentage: 0 }
  ]

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <div
                key={index}
                className="text-center p-6 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-300"
              >
                <div className={`w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg`}>
                  <IconComponent className={`w-8 h-8 ${stat.color}`} />
                </div>
                
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {stat.value}
                </div>
                
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {stat.label}
                </h3>
                
                <p className="text-gray-600 text-sm">
                  {stat.description}
                </p>
              </div>
            )
          })}
        </div>

        {/* Rating distribution */}
        <div className="bg-gray-50 rounded-2xl p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">
                Rozkład ocen
              </h3>
              
              <div className="space-y-3">
                {ratingDistribution.map((rating) => (
                  <div key={rating.stars} className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1 w-16">
                      <span className="text-sm font-medium text-gray-700">{rating.stars}</span>
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    </div>
                    
                    <div className="flex-1 bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-3 rounded-full transition-all duration-500"
                        style={{ width: `${rating.percentage}%` }}
                      />
                    </div>
                    
                    <div className="text-sm text-gray-600 w-12 text-right">
                      {rating.count}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="text-center lg:text-left">
              <div className="inline-flex items-center space-x-2 mb-4">
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-8 h-8 text-yellow-400 fill-current" />
                  ))}
                </div>
                <span className="text-4xl font-bold text-gray-900">4.9</span>
              </div>
              
              <p className="text-gray-600 mb-6">
                Nasze oceny mówią same za siebie. 98% klientów poleca nasze usługi 
                znajomym i rodzinie.
              </p>
              
              <div className="grid grid-cols-2 gap-4 text-center">
                <div className="bg-white rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-600 mb-1">127</div>
                  <div className="text-sm text-gray-600">Wszystkich opinii</div>
                </div>
                <div className="bg-white rounded-lg p-4">
                  <div className="text-2xl font-bold text-blue-600 mb-1">121</div>
                  <div className="text-sm text-gray-600">Pozytywnych</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ReviewsStats
